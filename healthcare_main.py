"""
Domain-Agnostic Healthcare Scheduling Framework - Example Implementation
Demonstrates the complete system with Google Maps integration and advanced features
"""

import datetime
import os
from typing import List

from healthcare_domain import (
    HealthcareResource, HealthcareTask, Location, TimeWindow, 
    HealthcareSkills, HealthcareServiceTypes, Priority, LocationType, SkillLevel
)
from healthcare_scheduler import HealthcareSchedulingEngine, SchedulingRequest
from google_maps_service import GoogleMapsHealthcareService


def create_sample_locations() -> List[Location]:
    """Create sample home healthcare locations - patient homes and agency office"""
    return [
        Location(
            id="agency_office",
            name="HomeCare Plus Agency Office",
            latitude=40.7128,
            longitude=-74.0060,
            location_type=LocationType.CLINIC,
            address="123 Main St, New York, NY 10001",
            timezone="America/New_York",
            safety_rating=5,
            accessibility_features=["wheelchair_accessible", "parking"],
            parking_available=True
        ),
        Location(
            id="patient_home_1",
            name="Patient Johnson Home",
            latitude=40.7589,
            longitude=-73.9851,
            location_type=LocationType.PATIENT_HOME,
            address="456 Oak Ave, New York, NY 10002",
            timezone="America/New_York",
            safety_rating=4,
            accessibility_features=["ground_floor"],
            parking_available=True
        ),
        Location(
            id="patient_home_2", 
            name="Patient Smith Home",
            latitude=40.7282,
            longitude=-73.7949,
            location_type=LocationType.PATIENT_HOME,
            address="789 Pine St, Queens, NY 11375",
            timezone="America/New_York",
            safety_rating=3,
            accessibility_features=[],
            parking_available=False
        ),
        Location(
            id="patient_home_3",
            name="Patient Williams Home",
            latitude=40.7831,
            longitude=-73.9712,
            location_type=LocationType.PATIENT_HOME,
            address="321 Elm Dr, Bronx, NY 10025",
            timezone="America/New_York",
            safety_rating=2,  # Lower safety area requiring pairing
            accessibility_features=["wheelchair_accessible"],
            parking_available=False
        )
    ]


def create_sample_resources() -> List[HealthcareResource]:
    """Create sample home healthcare clinicians"""

    # Get location references - home care clinicians start from agency office
    agency_office = Location("agency_office", "Agency Office", 40.7128, -74.0060, LocationType.CLINIC, "123 Main St")
    
    # Create availability windows
    morning_shift = TimeWindow(
        start=datetime.datetime(2025, 1, 15, 8, 0),
        end=datetime.datetime(2025, 1, 15, 16, 0),
        timezone="America/New_York"
    )
    
    afternoon_shift = TimeWindow(
        start=datetime.datetime(2025, 1, 15, 12, 0),
        end=datetime.datetime(2025, 1, 15, 20, 0),
        timezone="America/New_York"
    )
    
    return [
        HealthcareResource(
            id="nurse_alice",
            name="Alice Johnson, RN",
            skills=[
                HealthcareSkills.WOUND_CARE_ADVANCED,
                HealthcareSkills.MEDICATION_IV,
                HealthcareSkills.MEDICATION_BASIC
            ],
            availability_windows=[morning_shift],
            location=agency_office,
            priority=3,
            license_number="RN123456",
            certifications=["IV_CERTIFIED", "WOUND_CARE_SPECIALIST"],
            specializations=["DIABETES_CARE", "WOUND_MANAGEMENT"],
            max_daily_hours=8,
            max_weekly_hours=40,
            can_supervise=True
        ),
        HealthcareResource(
            id="nurse_bob",
            name="Bob Smith, LPN",
            skills=[
                HealthcareSkills.WOUND_CARE_BASIC,
                HealthcareSkills.MEDICATION_BASIC
            ],
            availability_windows=[afternoon_shift],
            location=agency_office,
            priority=5,
            license_number="LPN789012",
            certifications=["BASIC_LIFE_SUPPORT"],
            max_daily_hours=8,
            max_weekly_hours=40,
            requires_supervision=True
        ),
        HealthcareResource(
            id="therapist_carol",
            name="Carol Davis, PT",
            skills=[HealthcareSkills.PHYSICAL_THERAPY],
            availability_windows=[morning_shift],
            location=agency_office,
            priority=2,
            license_number="PT345678",
            certifications=["PHYSICAL_THERAPY_LICENSE"],
            specializations=["ORTHOPEDIC_REHAB", "GERIATRIC_CARE"],
            max_daily_hours=8,
            max_weekly_hours=40
        )
    ]


def create_sample_tasks() -> List[HealthcareTask]:
    """Create sample home healthcare tasks (patient home visits)"""

    # Get location references
    locations = create_sample_locations()
    patient_home_1 = locations[1]  # Johnson home
    patient_home_2 = locations[2]  # Smith home
    patient_home_3 = locations[3]  # Williams home (unsafe area)
    
    # Create time windows
    morning_window = TimeWindow(
        start=datetime.datetime(2025, 1, 15, 9, 0),
        end=datetime.datetime(2025, 1, 15, 12, 0),
        timezone="America/New_York",
        is_preferred=True
    )
    
    afternoon_window = TimeWindow(
        start=datetime.datetime(2025, 1, 15, 13, 0),
        end=datetime.datetime(2025, 1, 15, 17, 0),
        timezone="America/New_York"
    )
    
    emergency_window = TimeWindow(
        start=datetime.datetime(2025, 1, 15, 8, 0),
        end=datetime.datetime(2025, 1, 15, 20, 0),
        timezone="America/New_York"
    )
    
    return [
        HealthcareTask(
            id="visit_wound_care_001",
            name="Advanced Wound Care - Johnson",
            duration_minutes=90,
            required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
            preferred_skills=[],
            time_windows=[morning_window],
            location=patient_home_1,
            priority=Priority.HIGH,
            patient_id="patient_johnson",
            service_type=HealthcareServiceTypes.WOUND_CARE,
            clinical_notes="Complex diabetic ulcer requiring advanced wound care",
            equipment_required=["wound_care_kit", "sterile_supplies"],
            infection_control_level="CONTACT_PRECAUTIONS"
        ),
        HealthcareTask(
            id="visit_medication_001",
            name="IV Medication Administration - Johnson",
            duration_minutes=45,
            required_skills=[HealthcareSkills.MEDICATION_IV],
            time_windows=[afternoon_window],
            location=patient_home_1,
            priority=Priority.ROUTINE,
            patient_id="patient_johnson",
            service_type=HealthcareServiceTypes.MEDICATION_ADMIN,
            clinical_notes="Daily IV antibiotic administration",
            equipment_required=["IV_supplies", "medication"],
            continuity_requirements=["same_clinician_as_wound_care"],
            dependencies=["visit_wound_care_001"]
        ),
        HealthcareTask(
            id="visit_basic_care_001",
            name="Basic Wound Care - Smith",
            duration_minutes=60,
            required_skills=[HealthcareSkills.WOUND_CARE_BASIC],
            time_windows=[morning_window, afternoon_window],
            location=patient_home_2,
            priority=Priority.ROUTINE,
            patient_id="patient_smith",
            service_type=HealthcareServiceTypes.WOUND_CARE,
            clinical_notes="Simple dressing change",
            equipment_required=["basic_wound_supplies"]
        ),
        HealthcareTask(
            id="visit_emergency_001",
            name="Emergency Home Visit - Williams",
            duration_minutes=120,
            required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED, HealthcareSkills.MEDICATION_IV],
            time_windows=[emergency_window],
            location=patient_home_3,  # Unsafe area requiring safety pairing
            priority=Priority.CRITICAL,
            patient_id="patient_williams",
            service_type=HealthcareServiceTypes.EMERGENCY_RESPONSE,
            clinical_notes="Emergency wound dehiscence requiring immediate home visit",
            equipment_required=["emergency_kit", "IV_supplies", "advanced_wound_care"]
        ),
        HealthcareTask(
            id="visit_therapy_001",
            name="Physical Therapy - Rehabilitation",
            duration_minutes=75,
            required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
            time_windows=[morning_window],
            location=patient_home_1,
            priority=Priority.ROUTINE,
            patient_id="patient_johnson",
            service_type=HealthcareServiceTypes.PHYSICAL_THERAPY,
            clinical_notes="Post-surgical mobility rehabilitation",
            equipment_required=["therapy_equipment"]
        )
    ]


def main():
    """Main demonstration of the home healthcare scheduling framework"""

    print("🏠 Home Healthcare Scheduling Framework")
    print("=" * 50)
    print("Optimizing clinician visits to patient homes")
    
    # Initialize the scheduling engine
    google_api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4')
    scheduler = HealthcareSchedulingEngine(google_api_key)
    
    # Create sample data
    locations = create_sample_locations()
    resources = create_sample_resources()
    tasks = create_sample_tasks()
    
    print(f"📍 Locations: {len(locations)}")
    print(f"👥 Resources: {len(resources)}")
    print(f"📋 Tasks: {len(tasks)}")
    print()
    
    # Create scheduling request
    request = SchedulingRequest(
        resources=resources,
        tasks=tasks,
        locations=locations,
        schedule_date=datetime.date(2025, 1, 15),
        optimization_time_seconds=30,
        include_travel_optimization=True
    )
    
    # Generate optimized schedule
    print("🔄 Optimizing schedule...")
    result = scheduler.create_schedule(request)
    
    # Display results
    print(f"✅ Optimization completed in {result.optimization_time_seconds:.2f} seconds")
    print(f"📊 Score: {result.score}")
    print()
    
    # Display schedule
    print("📅 OPTIMIZED SCHEDULE")
    print("-" * 40)
    
    for resource in resources:
        resource_tasks = result.schedule.get_tasks_for_resource(resource)
        if resource_tasks:
            print(f"\n👤 {resource.name}")
            for task in sorted(resource_tasks, key=lambda t: t.start_time or datetime.datetime.min):
                if task.start_time and task.location:
                    end_time = task.get_end_time()
                    if end_time:
                        print(f"  {task.start_time.strftime('%H:%M')}-{end_time.strftime('%H:%M')} | "
                              f"{task.name} | {task.location.name}")
    
    # Display unassigned tasks
    unassigned = result.schedule.get_unassigned_tasks()
    if unassigned:
        print(f"\n⚠️  UNASSIGNED TASKS ({len(unassigned)})")
        for task in unassigned:
            print(f"  • {task.name} (Priority: {task.priority.name})")
    
    # Display statistics
    print(f"\n📈 STATISTICS")
    print("-" * 20)
    stats = result.statistics
    print(f"Assignment Rate: {stats['assignment_rate']:.1f}%")
    print(f"Average Resource Utilization: {stats['average_utilization']:.1f}%")
    
    # Display travel summary
    if result.travel_summary:
        print(f"\n🚗 TRAVEL SUMMARY")
        print("-" * 20)
        travel = result.travel_summary
        print(f"Total Distance: {travel['total_distance_km']:.1f} km")
        print(f"Total Travel Time: {travel['total_duration_hours']:.1f} hours")
        print(f"Estimated Fuel Cost: ${travel['estimated_total_fuel_cost']:.2f}")
    
    # Display recommendations
    if result.recommendations:
        print(f"\n💡 RECOMMENDATIONS")
        print("-" * 20)
        for i, rec in enumerate(result.recommendations, 1):
            print(f"{i}. {rec}")
    
    print(f"\n🎯 Home Healthcare Framework successfully demonstrated!")
    print("This system optimizes clinician routes for efficient patient home visits.")


if __name__ == '__main__':
    main()
