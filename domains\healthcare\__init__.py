"""
Healthcare Domain Implementation
===============================

This package provides healthcare-specific scheduling components that extend
the core framework for home healthcare agencies and medical scheduling.

Key Components:
- HealthcareResource: Clinicians, nurses, therapists with medical skills
- HealthcareTask: Patient visits, procedures with clinical requirements
- HealthcareConstraints: Medical compliance, safety, and regulatory constraints
- HealthcareSchedulingEngine: Specialized engine with Google Maps integration

Features:
- Medical skill hierarchies and certifications
- Infection control and safety protocols
- Regulatory compliance (rest periods, licensing)
- Real-time planning for emergencies and changes
- Google Maps integration for home visit routing
- Timezone-aware scheduling across regions
"""

__version__ = "1.0.0"
__domain__ = "healthcare"

# Healthcare-specific exports
from .domain import (
    HealthcareResource,
    HealthcareTask, 
    HealthcareSkills,
    HealthcareServiceTypes,
    InfectionControlLevel
)

from .constraints import HealthcareConstraintProvider

from .scheduler import HealthcareSchedulingEngine

from .integrations import GoogleMapsHealthcareService

__all__ = [
    'HealthcareResource',
    'HealthcareTask',
    'HealthcareSkills', 
    'HealthcareServiceTypes',
    'InfectionControlLevel',
    'HealthcareConstraintProvider',
    'HealthcareSchedulingEngine',
    'GoogleMapsHealthcareService'
]
