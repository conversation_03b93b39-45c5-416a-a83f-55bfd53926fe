# Clinician Scheduler - TimeFold Python Implementation

## Table of Contents
1. [Overview](#overview)
2. [Java vs Python Concepts](#java-vs-python-concepts)
3. [TimeFold Solver Concepts](#timefold-solver-concepts)
4. [Project Architecture](#project-architecture)
5. [Domain Model](#domain-model)
6. [Constraints System](#constraints-system)
7. [Solver Configuration](#solver-configuration)
8. [Code Walkthrough](#code-walkthrough)
9. [Running the Application](#running-the-application)
10. [Extending the System](#extending-the-system)

## Overview

This is a **clinician scheduling optimization system** built with TimeFold Solver in Python. Think of it as an intelligent scheduler that automatically assigns healthcare visits to clinicians while respecting various business rules and constraints.

### What does it solve?
- **Problem**: Schedule patient visits to available clinicians
- **Goal**: Find the optimal assignment that satisfies all hard constraints
- **Optimization**: Minimize constraint violations and maximize efficiency

### Real-world analogy
Imagine you're a dispatcher at a healthcare agency. You have:
- Multiple clinicians with different skills and availability
- Patient visits that need specific skills and have time windows
- Business rules (no overlapping visits, skill matching, etc.)

Instead of manually creating schedules (which is time-consuming and error-prone), this system automatically finds the best possible schedule.

## Java vs Python Concepts

As a Java developer, here are the key differences you'll encounter:

### 1. **Class Definitions**
```python
# Python - No explicit access modifiers, duck typing
class Clinician:
    def __init__(self, id_: int, name: str):  # Constructor
        self.id = id_                         # Instance variable
        self.name = name
```

```java
// Java equivalent
public class Clinician {
    private int id;
    private String name;
    
    public Clinician(int id, String name) {
        this.id = id;
        this.name = name;
    }
}
```

### 2. **Data Classes (Python) vs POJOs (Java)**
```python
# Python @dataclass - auto-generates constructor, toString, equals, etc.
@dataclass
class Visit:
    id: int
    patient_name: str
    duration_minutes: int
```

```java
// Java equivalent (with Lombok or manual implementation)
public class Visit {
    private int id;
    private String patientName;
    private int durationMinutes;
    // + constructor, getters, setters, toString, equals, hashCode
}
```

### 3. **Collections and Generics**
```python
# Python - Dynamic typing, no explicit generics
clinicians: List[Clinician] = []
skills: Set[str] = set()
```

```java
// Java - Static typing with generics
List<Clinician> clinicians = new ArrayList<>();
Set<String> skills = new HashSet<>();
```

### 4. **Annotations/Decorators**
```python
# Python decorators (similar to Java annotations)
@planning_entity
@dataclass
class Visit:
    pass
```

```java
// Java annotations
@PlanningEntity
public class Visit {
}
```

## TimeFold Solver Concepts

TimeFold is a constraint satisfaction solver. Here are the core concepts:

### 1. **Planning Problem Structure**
```
Planning Solution (Schedule)
├── Problem Facts (Clinicians, Time slots)
├── Planning Entities (Visits) 
└── Planning Variables (clinician, start_time)
```

### 2. **Key Components**

| Component | Purpose | Java Analogy |
|-----------|---------|--------------|
| `@planning_solution` | Root object containing the entire problem | Main data structure |
| `@planning_entity` | Objects to be optimized | Entities to be assigned |
| `@PlanningVariable` | Variables the solver can change | Fields to optimize |
| `@constraint_provider` | Business rules | Validation logic |

### 3. **Solver Process**
1. **Input**: Unassigned planning entities
2. **Processing**: Solver tries different combinations
3. **Evaluation**: Each combination gets a score based on constraints
4. **Output**: Best solution found within time limit

## Project Architecture

```
clinician-scheduler-python/
├── domain.py          # Domain model (entities, value objects)
├── constraints.py     # Business rules and scoring
├── main.py           # Application entry point
└── requirements.txt  # Dependencies
```

### Architecture Pattern
This follows the **Domain-Driven Design** pattern:
- **Domain Layer**: Core business entities (`domain.py`)
- **Application Layer**: Orchestration and configuration (`main.py`)
- **Infrastructure Layer**: Constraints and rules (`constraints.py`)

## Domain Model

### Class Hierarchy and Relationships

```
┌─────────────────────────────────────────────────────────────────┐
│                            Schedule                             │
│  ┌─────────────────────────────────────────────────────────────┤
│  │ + clinicians: List[Clinician]                               │
│  │ + visits: List[Visit]                                       │
│  │ + time_range: List[datetime]                                │
│  │ + score: HardSoftScore                                      │
│  └─────────────────────────────────────────────────────────────┤
└─────────────────────────┬───────────────────┬───────────────────┘
                          │                   │
                          ▼                   ▼
              ┌─────────────────────┐ ┌─────────────────────┐
              │      Clinician      │ │        Visit        │
              ├─────────────────────┤ ├─────────────────────┤
              │ + id: int           │ │ + id: int           │
              │ + name: str         │ │ + patient_name: str │
              │ + skills: Set[str]  │ │ + required_skill    │
              │ + service_areas     │ │ + service_area      │
              │ + availability      │ │ + window_start      │
              │ + max_daily_visits  │ │ + window_end        │
              └─────────────────────┘ │ + duration_minutes  │
                          │           │ + fixed_time: bool  │
                          │           │ + dependencies      │
                          │           │ + clinician ◄───────┼──┐
                          │           │ + start: datetime   │  │
                          │           └─────────────────────┘  │
                          │                       │            │
                          ▼                       ▼            │
              ┌─────────────────────┐ ┌─────────────────────┐  │
              │  AvailabilitySlot   │ │     Dependency      │  │
              ├─────────────────────┤ ├─────────────────────┤  │
              │ + start: datetime   │ │ + predecessor_id    │  │
              │ + end: datetime     │ │ + same_clinician_   │  │
              │ + contains_range()  │ │   required: bool    │  │
              └─────────────────────┘ └─────────────────────┘  │
                          │                                    │
                          ▼                                    │
              ┌─────────────────────┐                          │
              │    ServiceArea      │ ◄────────────────────────┘
              ├─────────────────────┤
              │ + code: str         │
              └─────────────────────┘
```

**Key Relationships:**
- **Schedule** contains multiple **Clinicians** and **Visits**
- **Visit** is assigned to one **Clinician** (planning variable)
- **Visit** belongs to one **ServiceArea**
- **Visit** can have multiple **Dependencies**
- **Clinician** has multiple **AvailabilitySlots**
- **Clinician** serves multiple **ServiceAreas**

### Detailed Domain Classes

#### 1. **Schedule** (Planning Solution)
```python
@planning_solution
@dataclass
class Schedule:
    clinicians: List[Clinician]  # Available resources
    visits: List[Visit]          # Tasks to be scheduled
    time_range: List[datetime]   # Available time slots
    score: Optional[HardSoftScore] = None  # Solution quality
```

**Java equivalent**:
```java
@PlanningSolution
public class Schedule {
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "clinicianRange")
    private List<Clinician> clinicians;

    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "timeRange")
    private List<LocalDateTime> timeRange;

    @PlanningEntityCollectionProperty
    private List<Visit> visits;

    @PlanningScore
    private HardSoftScore score;

    // Constructors, getters, setters...
}
```

**Key Point**: Both `clinicianRange` and `timeRange` are essential because:
- `clinicianRange` provides the possible clinicians that can be assigned to visits
- `timeRange` provides the possible time slots when visits can start
- These correspond to the two planning variables in the `Visit` entity: `clinician` and `start`

#### 2. **Visit** (Planning Entity)
```python
@planning_entity
@dataclass
class Visit:
    # Fixed properties (problem facts)
    id: int
    patient_name: str
    required_skill: str
    service_area: ServiceArea
    window_start: datetime
    window_end: datetime
    duration_minutes: int
    fixed_time: bool
    dependencies: List[Dependency]
    
    # Planning variables (what the solver optimizes)
    clinician: Optional[Clinician] = None    # WHO performs the visit
    start: Optional[datetime] = None         # WHEN the visit starts
```

**Key insight**: The solver will try different values for `clinician` and `start` to find the best assignment.

#### 3. **Clinician** (Problem Fact)
```python
class Clinician:
    def __init__(self, id_: int, name: str, skills: Set[str],
                 service_areas: Set[ServiceArea],
                 availability: List[AvailabilitySlot],
                 max_daily_visits: int = 5):
```

**Think of it as**: A resource with capabilities and constraints.

#### 4. **Dependency** (Business Rule)
```python
class Dependency:
    def __init__(self, predecessor_id: int, same_clinician_required: bool):
        self.predecessor_id = predecessor_id
        self.same_clinician_required = same_clinician_required
```

**Examples**:
- `Dependency(1, False)`: Visit must happen after visit #1, any clinician OK
- `Dependency(2, True)`: Visit must happen after visit #2, same clinician required

## Constraints System

Constraints define the business rules. They're evaluated for every possible solution and return a score.

### Score Types
- **Hard Constraints**: Must be satisfied (business rules)
- **Soft Constraints**: Preferred but not required (optimization goals)

### Constraint Implementation Pattern

```python
def constraint_name(constraint_factory):
    return (constraint_factory
            .for_each(EntityClass)           # For each entity
            .filter(violation_condition)     # Check if rule is violated
            .penalize(penalty_score)         # Apply penalty
            .as_constraint('Constraint Name'))
```

### Detailed Constraint Analysis

#### 1. **Skill Mismatch** (Hard Constraint)
```python
def skill_mismatch(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None and
                    v.required_skill not in v.clinician.skills)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill mismatch'))
```

**Business Rule**: A clinician can only perform visits that match their skills.
**Violation**: When a visit requiring "PHYSICAL_THERAPY" is assigned to a clinician who only has "SKILLED_NURSING".

#### 2. **Clinician Overlap** (Hard Constraint)
```python
def clinician_overlap(cf):
    def overlap(v1, v2):
        if v1.clinician != v2.clinician or v1.start is None or v2.start is None:
            return False
        return not (v1.start + timedelta(minutes=v1.duration_minutes) <= v2.start or
                    v2.start + timedelta(minutes=v2.duration_minutes) <= v1.start)

    return (cf.for_each_unique_pair(Visit)
            .filter(overlap)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Visits overlap'))
```

**Business Rule**: A clinician cannot be in two places at once.
**Logic**: Two visits overlap if neither ends before the other starts.

#### 3. **Dependency Order** (Hard Constraint)
```python
def dependency_order(cf):
    def is_dependency(pre, post):
        return any(d.predecessor_id == pre.id for d in post.dependencies)

    def order_violated(pre, post):
        return (pre.start is not None and post.start is not None and 
                post.start < pre.start + timedelta(minutes=pre.duration_minutes))

    return (cf.for_each(Visit)
            .join(Visit, Joiners.filtering(is_dependency))
            .filter(order_violated)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order'))
```

**Business Rule**: Dependent visits must happen in the correct order.
**Example**: If visit B depends on visit A, then B cannot start until A is completely finished.

## Solver Configuration

### Configuration Breakdown
```python
solver_config = SolverConfig(
    solution_class=Schedule,                    # What we're optimizing
    entity_class_list=[Visit],                  # What entities to assign
    score_director_factory_config=ScoreDirectorFactoryConfig(
        constraint_provider_function=constraints.define_constraints
    ),                                          # How to score solutions
    termination_config=TerminationConfig(
        spent_limit=Duration(seconds=10)        # When to stop
    )
)
```

### Solver Process Flow
1. **Initialization**: Create initial solution (possibly with unassigned entities)
2. **Search**: Try different assignments using metaheuristics
3. **Evaluation**: Score each solution using constraints
4. **Iteration**: Keep improving until termination condition
5. **Return**: Best solution found

## Code Walkthrough

Let's trace through the execution step by step:

### 1. **Problem Setup** (`build_problem()`)
```python
def build_problem() -> Schedule:
    # Create service area
    hou = ServiceArea('HOU')

    # Generate time slots (8:00 AM to 5:45 PM, every 15 minutes)
    time_range = []
    t = datetime.datetime(2025, 6, 1, 8, 0)
    while t <= datetime.datetime(2025, 6, 1, 17, 45):
        time_range.append(t)
        t += datetime.timedelta(minutes=15)
```

**Result**: 40 time slots (10 hours × 4 slots per hour)

### 2. **Create Clinicians**
```python
clins = [
    Clinician(1, 'Alice', {Skill.SKILLED_NURSING}, {hou},
              [AvailabilitySlot(datetime(2025,6,1,8,0), datetime(2025,6,1,18,0))]),
    Clinician(2, 'Bob', {Skill.SKILLED_NURSING}, {hou},
              [AvailabilitySlot(datetime(2025,6,1,8,0), datetime(2025,6,1,18,0))])
]
```

**Result**: 2 clinicians, both available 8 AM - 6 PM, both skilled in nursing

### 3. **Create Dependencies**
```python
dep_ab = Dependency(1, False)      # Visit B must happen after Visit A (any clinician)
dep_bc_same = Dependency(2, True)  # Visit C must happen after Visit B (same clinician)
```

### 4. **Create Visits**
```python
visits = [
    Visit(1, 'A', Skill.SKILLED_NURSING, hou,
          datetime(2025,6,1,9,0), datetime(2025,6,1,12,0), 60, False, []),
    Visit(2, 'B', Skill.SKILLED_NURSING, hou,
          datetime(2025,6,1,10,0), datetime(2025,6,1,15,0), 45, False, [dep_ab]),
    Visit(3, 'C', Skill.SKILLED_NURSING, hou,
          datetime(2025,6,1,11,0), datetime(2025,6,1,16,0), 30, False, [dep_bc_same])
]
```

**Dependency Chain**: A → B → C (where B→C requires same clinician)

### 5. **Solver Execution**
```python
solver = solver_factory.build_solver()
solved = solver.solve(schedule)
```

**What happens internally**:
1. Solver tries different combinations of (clinician, start_time) for each visit
2. Each combination is scored using the constraints
3. Better solutions are kept, worse ones discarded
4. Process continues until time limit (10 seconds)

### 6. **Output**
```python
print("Score:", solved.score)
for v in sorted(solved.visits, key=lambda v: v.start):
    print(v.start, v.patient_name, "->", v.clinician)
```

**Example output**:
```
Score: 0hard/0soft
2025-06-01 09:00:00 A -> Alice
2025-06-01 10:00:00 B -> Alice
2025-06-01 10:45:00 C -> Alice
```

## Running the Application

### Prerequisites
```bash
pip install timefold==1.22.1b0
```

### Execution
```bash
python main.py
```

### Expected Behavior
1. **Setup**: Creates problem with 2 clinicians and 3 visits
2. **Solving**: Runs for 10 seconds maximum
3. **Output**: Shows final score and visit assignments

### Understanding the Output

#### Score Interpretation
- `0hard/0soft`: Perfect solution (no constraint violations)
- `1hard/0soft`: One hard constraint violated (invalid solution)
- `0hard/5soft`: Valid solution with 5 soft constraint violations

#### Visit Assignment Format
```
2025-06-01 09:00:00 A -> Alice
│                   │    │
│                   │    └── Assigned clinician
│                   └────── Patient name
└────────────────────────── Start time
```

## Extending the System

### Adding New Constraints

#### Example: Travel Time Between Visits
```python
def travel_time_constraint(cf):
    def same_clinician_consecutive(v1, v2):
        return (v1.clinician == v2.clinician and
                v1.clinician is not None and
                v1.start is not None and v2.start is not None)

    def insufficient_travel_time(v1, v2):
        # Assume 15 minutes travel time between different locations
        v1_end = v1.start + timedelta(minutes=v1.duration_minutes)
        return v2.start < v1_end + timedelta(minutes=15)

    return (cf.for_each_unique_pair(Visit)
            .filter(same_clinician_consecutive)
            .filter(insufficient_travel_time)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Insufficient travel time'))
```

### Adding New Domain Objects

#### Example: Equipment Requirements
```python
@dataclass
class Equipment:
    id: int
    name: str
    available_locations: Set[ServiceArea]

@dataclass
class Visit:
    # ... existing fields ...
    required_equipment: Optional[Equipment] = None
```

### Performance Tuning

#### Solver Configuration Options
```python
solver_config = SolverConfig(
    # ... existing config ...
    termination_config=TerminationConfig(
        spent_limit=Duration(seconds=30),           # Longer solving time
        best_score_limit=HardSoftScore.ZERO,       # Stop when perfect solution found
        unimproved_spent_limit=Duration(seconds=5) # Stop if no improvement
    )
)
```

### Common Patterns

#### 1. **Conditional Constraints**
```python
def weekend_premium(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.start is not None and v.start.weekday() >= 5)  # Weekend
            .reward(HardSoftScore.ONE_SOFT)  # Prefer weekend assignments
            .as_constraint('Weekend premium'))
```

#### 2. **Aggregation Constraints**
```python
def balanced_workload(cf):
    return (cf.for_each(Visit)
            .group_by(lambda v: v.clinician, ConstraintCollectors.sum(lambda v: v.duration_minutes))
            .penalize(HardSoftScore.ONE_SOFT,
                     lambda clinician, total_minutes: abs(total_minutes - 240))  # Target 4 hours
            .as_constraint('Balanced workload'))
```

This documentation should give you a solid foundation for understanding both the Python syntax and the TimeFold concepts. The key is to think of TimeFold as an intelligent search engine that tries millions of combinations to find the best solution according to your business rules.
