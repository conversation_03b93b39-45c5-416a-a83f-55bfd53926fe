# Healthcare Constraint System

## Overview

The constraint system is the heart of the healthcare scheduling framework, implementing business rules and optimization goals using TimeFold Solver's constraint stream API. This system translates complex healthcare requirements into mathematical constraints that guide the optimization process.

## Constraint Architecture

### Constraint Categories

```
Healthcare Constraints
├── Hard Constraints (Feasibility)
│   ├── Resource Management
│   │   ├── Skill Requirements
│   │   ├── Capacity Limits
│   │   └── Availability Windows
│   ├── Temporal Constraints
│   │   ├── Time Windows
│   │   ├── Dependencies
│   │   └── Regulatory Breaks
│   ├── Spatial Constraints
│   │   ├── Location Access
│   │   ├── Travel Limits
│   │   └── Safety Areas
│   └── Healthcare Rules
│       ├── Infection Control
│       ├── Supervision Requirements
│       └── Emergency Protocols
└── Soft Constraints (Optimization)
    ├── Efficiency Goals
    │   ├── Travel Minimization
    │   ├── Workload Balance
    │   └── Cost Optimization
    ├── Quality Goals
    │   ├── Continuity of Care
    │   ├── Skill Matching
    │   └── Patient Preferences
    └── Operational Goals
        ├── Resource Utilization
        ├── Schedule Stability
        └── Emergency Readiness
```

## Hard Constraints (Must Be Satisfied)

### 1. Skill Requirement Violation

**Purpose**: Ensure resources have the required skills for assigned tasks.

```python
def skill_requirement_violation(cf):
    """
    Hard constraint: Resource must have required skills for the task
    Supports hierarchical skill matching with substitution
    """
    def lacks_required_skill(task):
        if task.assigned_resource is None:
            return False
        
        for required_skill in task.required_skills:
            if not task.assigned_resource.can_perform_skill(required_skill):
                return True
        return False
    
    return (cf.for_each(HealthcareTask)
            .filter(lacks_required_skill)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill requirement violation'))
```

**Healthcare Context**:
- **Nursing Skills**: IV administration requires IV certification
- **Therapy Skills**: Physical therapy requires PT license
- **Skill Hierarchy**: Advanced wound care can substitute for basic wound care
- **Certification Tracking**: Expired certifications prevent assignment

**Example Violation**:
```python
# Task requires IV medication administration
iv_task = HealthcareTask(
    required_skills=[HealthcareSkills.MEDICATION_IV],
    # ... other properties
)

# Resource only has basic medication skills
basic_nurse = HealthcareResource(
    skills=[HealthcareSkills.MEDICATION_BASIC],
    # ... other properties
)

# This assignment would violate the skill requirement constraint
```

### 2. Resource Capacity Exceeded

**Purpose**: Prevent overloading resources beyond their capacity limits.

```python
def resource_capacity_exceeded(cf):
    """
    Hard constraint: Resource cannot exceed daily/weekly capacity limits
    Considers both task count and total duration
    """
    def exceeds_daily_capacity(resource, tasks):
        if resource is None or not tasks:
            return False
        
        total_duration = sum(task.duration_minutes for task in tasks)
        max_duration = resource.max_daily_hours * 60
        
        return total_duration > max_duration
    
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.assigned_resource is not None)
            .group_by(lambda task: task.assigned_resource, ConstraintCollectors.to_list())
            .filter(exceeds_daily_capacity)
            .penalize(HardSoftScore.ONE_HARD, 
                     lambda resource, tasks: calculate_overtime_penalty(resource, tasks))
            .as_constraint('Resource capacity exceeded'))
```

**Healthcare Context**:
- **Labor Laws**: Maximum 12-hour shifts for nurses
- **Union Rules**: Mandatory break periods
- **Fatigue Management**: Prevent burnout and errors
- **Overtime Costs**: Financial implications of exceeding capacity

### 3. Time Window Violation

**Purpose**: Ensure tasks are scheduled within their allowed time windows.

```python
def time_window_violation(cf):
    """
    Hard constraint: Task must be scheduled within allowed time windows
    Supports multiple time windows and flexibility margins
    """
    def violates_time_window(task):
        if task.start_time is None or not task.time_windows:
            return False
        
        task_end = task.get_end_time()
        
        # Check if task fits in any allowed time window
        for window in task.time_windows:
            # Consider flexibility margin
            window_start = window.start - timedelta(minutes=window.flexibility_minutes)
            window_end = window.end + timedelta(minutes=window.flexibility_minutes)
            
            if window_start <= task.start_time and task_end <= window_end:
                return False
        
        return True
    
    return (cf.for_each(HealthcareTask)
            .filter(violates_time_window)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Time window violation'))
```

**Healthcare Context**:
- **Patient Availability**: Patient only available mornings
- **Clinical Requirements**: Medication must be given at specific times
- **Facility Hours**: Clinic only open during business hours
- **Emergency Flexibility**: Critical tasks may have wider windows

### 4. Resource Double Booking

**Purpose**: Prevent assigning a resource to overlapping tasks.

```python
def resource_double_booking(cf):
    """
    Hard constraint: Resource cannot be assigned to overlapping tasks
    Includes travel time between locations
    """
    def tasks_overlap_with_travel(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or 
            task1.assigned_resource is None or
            task1.start_time is None or task2.start_time is None):
            return False
        
        # Calculate task end times
        task1_end = task1.get_end_time()
        task2_end = task2.get_end_time()
        
        # Add travel time if different locations
        travel_buffer = timedelta(minutes=0)
        if task1.location and task2.location and task1.location.id != task2.location.id:
            # Get travel time from Google Maps service
            travel_info = maps_service.get_travel_info(task1.location, task2.location)
            travel_buffer = timedelta(seconds=travel_info.duration_seconds)
        
        # Check for overlap considering travel time
        if task1_end <= task2.start_time:
            return task1_end + travel_buffer > task2.start_time
        elif task2_end <= task1.start_time:
            return task2_end + travel_buffer > task1.start_time
        else:
            return True  # Tasks overlap in time
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(tasks_overlap_with_travel)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Resource double booking'))
```

### 5. Dependency Order Violation

**Purpose**: Ensure dependent tasks are scheduled in correct order.

```python
def dependency_order_violation(cf):
    """
    Hard constraint: Dependent tasks must be scheduled in correct order
    Supports both temporal and resource dependencies
    """
    def violates_dependency_order(predecessor, successor):
        if (predecessor.start_time is None or successor.start_time is None):
            return False
        
        # Find dependency relationship
        dependency = next(
            (dep for dep in successor.dependencies if dep == predecessor.id),
            None
        )
        
        if not dependency:
            return False
        
        predecessor_end = predecessor.get_end_time()
        
        # Check temporal dependency
        if successor.start_time < predecessor_end:
            return True
        
        # Check same-clinician dependency if required
        if (hasattr(dependency, 'same_clinician_required') and 
            dependency.same_clinician_required and
            predecessor.assigned_resource != successor.assigned_resource):
            return True
        
        return False
    
    return (cf.for_each(HealthcareTask)
            .join(HealthcareTask, 
                  Joiners.filtering(lambda pred, succ: succ.id in pred.dependencies))
            .filter(violates_dependency_order)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order violation'))
```

## Soft Constraints (Optimization Goals)

### 1. Minimize Travel Distance

**Purpose**: Reduce travel time and costs between healthcare visits.

```python
def minimize_travel_distance(cf):
    """
    Soft constraint: Minimize travel distance between consecutive tasks
    Uses real Google Maps data for accurate calculations
    """
    def calculate_travel_penalty(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or
            task1.assigned_resource is None or
            task1.location is None or task2.location is None):
            return 0
        
        # Get real travel data from Google Maps
        travel_info = maps_service.get_travel_info(task1.location, task2.location)
        
        # Convert to penalty points (1 point per minute of travel)
        travel_minutes = travel_info.duration_seconds / 60
        
        # Apply healthcare-specific adjustments
        penalty = int(travel_minutes)
        
        # Extra penalty for unsafe areas
        if task2.location.safety_rating < 3:
            penalty *= 1.5
        
        # Reduced penalty for accessible locations
        if "wheelchair_accessible" in task2.location.accessibility_features:
            penalty *= 0.8
        
        return penalty
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(lambda t1, t2: t1.assigned_resource == t2.assigned_resource and
                                   t1.assigned_resource is not None)
            .penalize(HardSoftScore.ONE_SOFT, calculate_travel_penalty)
            .as_constraint('Minimize travel distance'))
```

### 2. Balance Workload Distribution

**Purpose**: Distribute work evenly across available resources.

```python
def balance_workload_distribution(cf):
    """
    Soft constraint: Balance workload across resources
    Considers both task count and total duration
    """
    def calculate_workload_imbalance(resource, tasks):
        if resource is None or not tasks:
            return 0
        
        # Calculate actual workload
        total_duration = sum(task.duration_minutes for task in tasks)
        task_count = len(tasks)
        
        # Calculate ideal workload (based on available resources)
        ideal_duration = resource.max_daily_hours * 60 * 0.75  # 75% utilization target
        ideal_task_count = 6  # Target tasks per day
        
        # Calculate imbalance penalties
        duration_deviation = abs(total_duration - ideal_duration) / 60  # Convert to hours
        count_deviation = abs(task_count - ideal_task_count)
        
        # Quadratic penalty for fairness (heavily penalize extreme imbalances)
        return int(duration_deviation ** 2 + count_deviation ** 2)
    
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.assigned_resource is not None)
            .group_by(lambda task: task.assigned_resource, ConstraintCollectors.to_list())
            .penalize(HardSoftScore.ONE_SOFT, calculate_workload_imbalance)
            .as_constraint('Balance workload distribution'))
```

### 3. Maximize Continuity of Care

**Purpose**: Prefer assigning related tasks to the same resource.

```python
def maximize_continuity_of_care(cf):
    """
    Soft constraint: Maximize continuity of care
    Prefers same clinician for related patient visits
    """
    def breaks_continuity(task1, task2):
        # Same patient, different resources breaks continuity
        if (task1.patient_id == task2.patient_id and 
            task1.patient_id != "" and
            task1.assigned_resource != task2.assigned_resource and
            task1.assigned_resource is not None and
            task2.assigned_resource is not None):
            
            # Calculate continuity penalty based on relationship strength
            penalty = 10  # Base penalty
            
            # Higher penalty for related services
            if (task1.service_type == task2.service_type or
                any(req in task1.continuity_requirements for req in ["same_clinician_preferred"])):
                penalty *= 2
            
            # Critical continuity for certain combinations
            if ("wound_care" in task1.service_type.lower() and 
                "medication" in task2.service_type.lower()):
                penalty *= 3
            
            return penalty
        
        return 0
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .penalize(HardSoftScore.ONE_SOFT, breaks_continuity)
            .as_constraint('Maximize continuity of care'))
```

### 4. Prefer Skill Exact Match

**Purpose**: Prefer exact skill matches over hierarchical substitutions.

```python
def prefer_skill_exact_match(cf):
    """
    Soft constraint: Prefer exact skill matches over substitutions
    Encourages efficient use of specialized skills
    """
    def uses_skill_substitution(task):
        if task.assigned_resource is None:
            return 0
        
        substitution_penalty = 0
        
        for required_skill in task.required_skills:
            # Check if we have exact match
            exact_match = any(s.id == required_skill.id for s in task.assigned_resource.skills)
            
            if not exact_match:
                # Using skill substitution - calculate penalty
                for resource_skill in task.assigned_resource.skills:
                    if (resource_skill.level.value > required_skill.level.value and 
                        required_skill.id in resource_skill.parent_skills):
                        # Penalty based on skill level difference
                        level_diff = resource_skill.level.value - required_skill.level.value
                        substitution_penalty += level_diff * 2
                        break
        
        return substitution_penalty
    
    return (cf.for_each(HealthcareTask)
            .penalize(HardSoftScore.ONE_SOFT, uses_skill_substitution)
            .as_constraint('Prefer skill exact match'))
```

## Healthcare-Specific Constraints

### 1. Safety Pairing Requirement

```python
def safety_pairing_violation(cf):
    """
    Healthcare-specific constraint: Require safety pairing in high-risk situations
    """
    def requires_safety_pairing(task):
        if task.assigned_resource is None or task.location is None:
            return False
        
        # High-risk conditions requiring pairing
        high_risk_conditions = [
            task.location.safety_rating <= 2,  # Unsafe area
            task.priority == Priority.CRITICAL,  # Emergency situation
            "high_risk" in task.clinical_notes.lower(),  # Clinical risk
            task.start_time and (task.start_time.hour < 6 or task.start_time.hour > 22)  # Night hours
        ]
        
        if any(high_risk_conditions):
            # Check if resource has a paired colleague nearby
            # This would require additional logic to find nearby resources
            return not has_safety_pair(task.assigned_resource, task.start_time, task.location)
        
        return False
    
    return (cf.for_each(HealthcareTask)
            .filter(requires_safety_pairing)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Safety pairing violation'))
```

### 2. Infection Control Compliance

```python
def infection_control_violation(cf):
    """
    Healthcare-specific constraint: Ensure infection control protocols
    """
    def violates_infection_control(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or
            task1.assigned_resource is None):
            return False
        
        # Check for infection control conflicts
        if (task1.infection_control_level == "CONTACT_PRECAUTIONS" and
            task2.infection_control_level == "STANDARD"):
            
            # Require time gap for decontamination
            time_gap = abs((task2.start_time - task1.get_end_time()).total_seconds() / 60)
            return time_gap < 30  # 30 minutes minimum gap
        
        return False
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(violates_infection_control)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Infection control violation'))
```

## Constraint Configuration

### 1. Configurable Weights

```python
@dataclass
class ConstraintConfiguration:
    """Domain-agnostic constraint configuration"""
    
    # Hard constraint weights (usually fixed)
    skill_mismatch: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    resource_conflict: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    time_window_violation: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    
    # Soft constraint weights (configurable for different scenarios)
    minimize_travel: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "1soft"))
    balance_workload: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "5soft"))
    prefer_continuity: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "10soft"))
    
    # Healthcare-specific weights
    safety_pairing: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    infection_control: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
```

### 2. Scenario-Based Configuration

```python
# Different configurations for different healthcare scenarios
healthcare_constraint_configs = {
    "home_healthcare": ConstraintConfiguration(
        minimize_travel=ConstraintWeight("0hard", "10soft"),  # High priority on travel
        prefer_continuity=ConstraintWeight("0hard", "15soft"),  # Very important for home care
        safety_pairing=ConstraintWeight("1hard")  # Required for safety
    ),
    
    "hospital_scheduling": ConstraintConfiguration(
        balance_workload=ConstraintWeight("0hard", "20soft"),  # Critical for staff satisfaction
        minimize_travel=ConstraintWeight("0hard", "2soft"),  # Less important in hospital
        infection_control=ConstraintWeight("1hard")  # Critical in hospital setting
    ),
    
    "emergency_response": ConstraintConfiguration(
        minimize_travel=ConstraintWeight("0hard", "50soft"),  # Extremely important for response time
        prefer_continuity=ConstraintWeight("0hard", "1soft"),  # Less important in emergencies
        safety_pairing=ConstraintWeight("1hard")  # Always required for emergencies
    )
}
```

This constraint system provides a flexible, configurable foundation for healthcare scheduling that can adapt to various scenarios while maintaining the essential business rules and optimization goals specific to healthcare operations.
