# Home Healthcare Agency Scheduling Framework

## 🏠 **Target Customer: Home-Based Healthcare Agencies**

This framework is specifically designed for **home healthcare agencies** that need to schedule clinicians for patient visits in their homes. It solves the complex logistics of routing healthcare professionals efficiently while maintaining quality care.

## 🎯 **Primary Use Cases for Home Healthcare Agencies**

### **1. Daily Clinician Scheduling**
- **Nurses** visiting patients for wound care, medication administration, assessments
- **Physical Therapists** providing in-home rehabilitation services
- **Home Health Aides** assisting with daily living activities
- **Specialized Clinicians** (IV therapy, wound specialists, etc.)

### **2. Route Optimization**
- **Minimize Travel Time** between patient homes using real Google Maps data
- **Reduce Fuel Costs** through efficient routing
- **Maximize Patient Visits** per day per clinician
- **Handle Traffic Delays** with real-time adjustments

### **3. Emergency Home Visits**
- **Urgent Patient Calls** requiring immediate home visits
- **Real-Time Rescheduling** when emergencies arise
- **Priority-Based Assignment** for critical situations
- **Safety Pairing** for high-risk areas or situations

### **4. Compliance & Safety**
- **Skill Matching** - ensure clinicians have required certifications
- **Safety Protocols** - required pairing for unsafe neighborhoods
- **Regulatory Compliance** - labor law adherence, break requirements
- **Infection Control** - proper spacing between high-risk visits

## 🚗 **Google Maps Integration Benefits for Home Healthcare**

Your Google Maps API key (`AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4`) enables:

### **Real-World Routing**
```python
# Calculate actual travel time between patient homes
travel_info = maps_service.get_travel_info(
    origin=patient_johnson_home,
    destination=patient_smith_home,
    departure_time=datetime.datetime(2025, 1, 15, 10, 0)
)

print(f"Travel time: {travel_info.duration_seconds / 60:.0f} minutes")
print(f"Distance: {travel_info.distance_meters / 1000:.1f} km")
print(f"With traffic: {travel_info.duration_in_traffic_seconds / 60:.0f} minutes")
```

### **Cost Optimization**
- **Fuel Cost Estimation** based on actual distances
- **Time Cost Calculation** for clinician hours
- **Efficiency Metrics** for agency management

### **Traffic-Aware Scheduling**
- **Real-Time Updates** when clinicians are delayed by traffic
- **Automatic Rescheduling** for significant delays
- **Patient Notifications** for appointment changes

## 📊 **Home Healthcare Specific Features**

### **1. Patient Home Visit Scheduling**
```python
# Example: Wound care visit at patient's home
wound_care_visit = HealthcareTask(
    id="wound_care_johnson",
    name="Advanced Wound Care - Mrs. Johnson",
    duration_minutes=90,
    required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
    location=patient_home_johnson,  # Patient's actual home address
    priority=Priority.HIGH,
    patient_id="patient_johnson",
    clinical_notes="Complex diabetic ulcer requiring advanced care",
    equipment_required=["wound_care_kit", "sterile_supplies"]
)
```

### **2. Clinician Route Optimization**
```python
# Optimize daily route for home healthcare nurse
daily_route = maps_service.optimize_healthcare_route(
    start_location=agency_office,
    visit_locations=[
        (patient_home_1, datetime.datetime(2025, 1, 15, 9, 0)),
        (patient_home_2, datetime.datetime(2025, 1, 15, 11, 0)),
        (patient_home_3, datetime.datetime(2025, 1, 15, 14, 0))
    ],
    end_location=agency_office
)

print(f"Total distance: {daily_route.total_distance_meters / 1000:.1f} km")
print(f"Estimated fuel cost: ${daily_route.estimated_fuel_cost:.2f}")
```

### **3. Safety Considerations for Home Visits**
```python
# Safety pairing for high-risk areas
unsafe_area_visit = HealthcareTask(
    id="emergency_visit_williams",
    name="Emergency Home Visit - Williams",
    location=patient_home_unsafe_area,  # Safety rating = 2
    priority=Priority.CRITICAL,
    clinical_notes="Emergency visit in high-risk neighborhood",
    # Framework automatically requires safety pairing
)
```

## 🏢 **Typical Home Healthcare Agency Workflow**

### **Morning Schedule Creation**
1. **Import Patient Visits** for the day
2. **Check Clinician Availability** and skills
3. **Run Optimization** with Google Maps integration
4. **Review Generated Routes** and make adjustments
5. **Send Schedules** to clinicians with driving directions

### **Real-Time Management**
1. **Monitor Traffic Delays** through Google Maps
2. **Handle Emergency Calls** with priority scheduling
3. **Adjust Routes** when patients cancel or reschedule
4. **Track Clinician Progress** throughout the day

### **End-of-Day Analytics**
1. **Calculate Total Miles** driven by all clinicians
2. **Analyze Efficiency Metrics** (visits per hour, travel time)
3. **Generate Cost Reports** (fuel, overtime, efficiency)
4. **Plan Improvements** for future scheduling

## 💰 **ROI for Home Healthcare Agencies**

### **Cost Savings**
- **Reduced Fuel Costs** through optimized routing (15-25% savings typical)
- **Increased Productivity** - more patient visits per day
- **Reduced Overtime** through better workload distribution
- **Lower Administrative Time** - automated scheduling vs manual

### **Quality Improvements**
- **Better Patient Care** through proper skill matching
- **Improved Clinician Safety** with safety pairing protocols
- **Reduced No-Shows** with optimized time windows
- **Enhanced Compliance** with regulatory requirements

### **Operational Benefits**
- **Real-Time Visibility** into clinician locations and schedules
- **Emergency Response** capability for urgent patient needs
- **Scalability** - easily add more clinicians and service areas
- **Data-Driven Decisions** with comprehensive analytics

## 🚀 **Getting Started for Home Healthcare Agencies**

### **Step 1: Basic Setup**
```bash
# Install framework
pip install timefold==1.22.1b0 googlemaps requests

# Set your Google Maps API key
export GOOGLE_API_KEY=AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4
```

### **Step 2: Configure for Your Agency**
```python
# Define your agency office location
agency_office = Location(
    id="your_agency_office",
    name="Your Home Healthcare Agency",
    latitude=your_latitude,
    longitude=your_longitude,
    location_type=LocationType.CLINIC,
    address="Your agency address"
)

# Define your clinicians
your_nurses = [
    HealthcareResource(
        id="nurse_1",
        name="Your Nurse Name",
        skills=[HealthcareSkills.WOUND_CARE_ADVANCED, HealthcareSkills.MEDICATION_IV],
        license_number="RN123456",
        max_daily_hours=8,
        location=agency_office
    )
]
```

### **Step 3: Import Patient Data**
```python
# Convert your patient visits to framework format
patient_visits = [
    HealthcareTask(
        id=f"visit_{patient_id}",
        name=f"Visit for {patient_name}",
        required_skills=[required_skill],
        location=patient_home_location,
        duration_minutes=visit_duration,
        priority=visit_priority
    )
    for patient_id, patient_name, required_skill, patient_home_location, 
        visit_duration, visit_priority in your_patient_data
]
```

### **Step 4: Run Optimization**
```bash
python healthcare_main.py
```

## 📞 **Support for Home Healthcare Agencies**

This framework is specifically designed with home healthcare agencies in mind. The examples, constraints, and optimizations all focus on the unique challenges of:

- **Patient home visits** rather than facility-based care
- **Travel optimization** between residential locations
- **Safety protocols** for home environments
- **Emergency response** for urgent home visits
- **Cost management** for mobile healthcare operations

The domain-agnostic design means it can be easily customized for your specific agency's needs while maintaining the core focus on home-based healthcare delivery.
