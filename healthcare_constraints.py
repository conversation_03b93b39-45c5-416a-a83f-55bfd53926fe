"""
Domain-Agnostic Healthcare Constraints
Based on TimeFold Solver constraint patterns adapted for healthcare scheduling
"""

import datetime
from typing import List, Tuple
from timefold.solver.score import constraint_provider, ConstraintCollectors, Joiners
from timefold.solver.score import HardSoftScore

from healthcare_domain import HealthcareTask, HealthcareResource, HealthcareSchedule, Priority


@constraint_provider
def define_healthcare_constraints(constraint_factory):
    """
    Define all healthcare scheduling constraints
    Following TimeFold's constraint provider pattern
    """
    return [
        # Hard Constraints (Must be satisfied)
        skill_requirement_violation(constraint_factory),
        resource_capacity_exceeded(constraint_factory),
        time_window_violation(constraint_factory),
        resource_double_booking(constraint_factory),
        location_accessibility_violation(constraint_factory),
        dependency_order_violation(constraint_factory),
        regulatory_compliance_violation(constraint_factory),
        safety_pairing_violation(constraint_factory),

        # Soft Constraints (Optimization goals)
        minimize_travel_distance(constraint_factory),
        balance_workload_distribution(constraint_factory),
        maximize_continuity_of_care(constraint_factory),
        prefer_skill_exact_match(constraint_factory),
        minimize_overtime_costs(constraint_factory),
        prioritize_high_priority_tasks(constraint_factory),
        prefer_resource_preferences(constraint_factory),
    ]


# ============================================================================
# Hard Constraints (Must be satisfied)
# ============================================================================

def skill_requirement_violation(cf):
    """
    Hard constraint: Resource must have required skills for the task
    Supports hierarchical skill matching
    """
    def lacks_required_skill(task):
        if task.assigned_resource is None:
            return False
        
        for required_skill in task.required_skills:
            if not task.assigned_resource.can_perform_skill(required_skill):
                return True
        return False
    
    return (cf.for_each(HealthcareTask)
            .filter(lacks_required_skill)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill requirement violation'))


def resource_capacity_exceeded(cf):
    """
    Hard constraint: Resource cannot be assigned more tasks than capacity allows
    """
    def exceeds_daily_capacity(resource, task_count):
        if resource is None:
            return False
        # Assuming each task represents work hours
        total_hours = task_count  # Simplified - would calculate actual duration
        return total_hours > resource.max_daily_hours
    
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.assigned_resource is not None)
            .group_by(lambda task: task.assigned_resource, ConstraintCollectors.count())
            .filter(exceeds_daily_capacity)
            .penalize(HardSoftScore.ONE_HARD, 
                     lambda resource, count: count - resource.max_daily_hours)
            .as_constraint('Resource capacity exceeded'))


def time_window_violation(cf):
    """
    Hard constraint: Task must be scheduled within its allowed time windows
    """
    def violates_time_window(task):
        if task.start_time is None or not task.time_windows:
            return False
        
        task_end = task.get_end_time()
        for window in task.time_windows:
            if window.start <= task.start_time and task_end <= window.end:
                return False
        return True
    
    return (cf.for_each(HealthcareTask)
            .filter(violates_time_window)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Time window violation'))


def resource_double_booking(cf):
    """
    Hard constraint: Resource cannot be assigned to overlapping tasks
    """
    def tasks_overlap(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or 
            task1.assigned_resource is None or
            task1.start_time is None or task2.start_time is None):
            return False
        
        task1_end = task1.get_end_time()
        task2_end = task2.get_end_time()
        
        # Check for overlap
        return not (task1_end <= task2.start_time or task2_end <= task1.start_time)
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(tasks_overlap)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Resource double booking'))


def location_accessibility_violation(cf):
    """
    Hard constraint: Resource must be able to access task location
    """
    def cannot_access_location(task):
        if task.assigned_resource is None or task.location is None:
            return False
        
        # Check if resource location is compatible with task location
        # This could include distance limits, transportation availability, etc.
        resource_location = task.assigned_resource.location
        if resource_location is None:
            return False
        
        # Example: Check if locations are in compatible service areas
        # In real implementation, this would use Google Maps API for distance/time
        return False  # Simplified for now
    
    return (cf.for_each(HealthcareTask)
            .filter(cannot_access_location)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Location accessibility violation'))


def dependency_order_violation(cf):
    """
    Hard constraint: Dependent tasks must be scheduled in correct order
    """
    def violates_dependency_order(task1, task2):
        if (task1.start_time is None or task2.start_time is None or
            task2.id not in task1.dependencies):
            return False
        
        # task2 depends on task1, so task1 must finish before task2 starts
        task1_end = task1.get_end_time()
        return task2.start_time < task1_end
    
    return (cf.for_each(HealthcareTask)
            .join(HealthcareTask, 
                  Joiners.filtering(lambda t1, t2: t2.id in t1.dependencies))
            .filter(violates_dependency_order)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order violation'))


def regulatory_compliance_violation(cf):
    """
    Hard constraint: Ensure regulatory compliance (rest periods, certifications, etc.)
    """
    def violates_rest_period(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or
            task1.assigned_resource is None or
            task1.start_time is None or task2.start_time is None):
            return False
        
        # Check minimum rest period between tasks (e.g., 30 minutes)
        min_rest_minutes = 30
        task1_end = task1.get_end_time()
        
        if task1_end < task2.start_time:
            rest_time = (task2.start_time - task1_end).total_seconds() / 60
            return rest_time < min_rest_minutes
        
        return False
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(violates_rest_period)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Regulatory compliance violation'))


def safety_pairing_violation(cf):
    """
    Hard constraint: Require safety pairing in high-risk situations
    Based on TimeFold employee pairing patterns we discussed
    """
    def requires_safety_pairing(task):
        if task.assigned_resource is None or task.location is None:
            return False

        # High-risk conditions requiring pairing (from our previous discussion)
        high_risk_conditions = [
            task.location.safety_rating <= 2,  # Unsafe area
            task.priority == Priority.CRITICAL,  # Emergency situation
            "high_risk" in task.clinical_notes.lower() if task.clinical_notes else False,
            task.start_time and (task.start_time.hour < 6 or task.start_time.hour > 22)  # Night hours
        ]

        if any(high_risk_conditions):
            # Check if resource has a paired colleague nearby
            # This implements the employee pairing pattern we analyzed from TimeFold
            return not has_safety_pair_available(task.assigned_resource, task.start_time, task.location)

        return False

    return (cf.for_each(HealthcareTask)
            .filter(requires_safety_pairing)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Safety pairing violation'))


def has_safety_pair_available(resource, time, location):
    """
    Helper function to check if safety pairing is available
    This implements the employee pairing logic we discussed
    """
    # Simplified implementation - in production this would:
    # 1. Find other resources available at the same time
    # 2. Check if they can be assigned to nearby locations
    # 3. Ensure proper pairing (e.g., male-female pairs for certain situations)
    return False  # For now, always require pairing for high-risk situations


# ============================================================================
# Soft Constraints (Optimization goals)
# ============================================================================

def minimize_travel_distance(cf):
    """
    Soft constraint: Minimize travel distance between consecutive tasks
    """
    def calculate_travel_penalty(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or
            task1.assigned_resource is None or
            task1.location is None or task2.location is None):
            return 0
        
        # Simplified distance calculation - would use Google Maps API in practice
        # For now, use a basic penalty based on different locations
        if task1.location.id != task2.location.id:
            return 10  # Base travel penalty
        return 0
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(lambda t1, t2: t1.assigned_resource == t2.assigned_resource and
                                   t1.assigned_resource is not None)
            .penalize(HardSoftScore.ONE_SOFT, calculate_travel_penalty)
            .as_constraint('Minimize travel distance'))


def balance_workload_distribution(cf):
    """
    Soft constraint: Balance workload across resources
    """
    def calculate_workload_imbalance(resource, task_count):
        if resource is None:
            return 0
        
        # Penalize deviation from ideal workload
        ideal_workload = 6  # tasks per day
        deviation = abs(task_count - ideal_workload)
        return deviation * deviation  # Quadratic penalty for fairness
    
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.assigned_resource is not None)
            .group_by(lambda task: task.assigned_resource, ConstraintCollectors.count())
            .penalize(HardSoftScore.ONE_SOFT, calculate_workload_imbalance)
            .as_constraint('Balance workload distribution'))


def maximize_continuity_of_care(cf):
    """
    Soft constraint: Prefer assigning related tasks to the same resource
    """
    def breaks_continuity(task1, task2):
        if (task1.assigned_resource == task2.assigned_resource or
            task1.patient_id != task2.patient_id or
            task1.patient_id == ""):
            return False
        
        # Same patient, different resources - breaks continuity
        return True
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(breaks_continuity)
            .penalize(HardSoftScore.of(0, 10))  # Higher penalty for continuity
            .as_constraint('Maximize continuity of care'))


def prefer_skill_exact_match(cf):
    """
    Soft constraint: Prefer exact skill matches over hierarchical substitutions
    """
    def uses_skill_substitution(task):
        if task.assigned_resource is None:
            return False
        
        # Check if using higher-level skill for lower-level task
        for required_skill in task.required_skills:
            exact_match = any(s.id == required_skill.id for s in task.assigned_resource.skills)
            if not exact_match:
                # Using skill substitution
                return True
        return False
    
    return (cf.for_each(HealthcareTask)
            .filter(uses_skill_substitution)
            .penalize(HardSoftScore.ONE_SOFT)
            .as_constraint('Prefer skill exact match'))


def minimize_overtime_costs(cf):
    """
    Soft constraint: Minimize overtime by keeping within regular hours
    """
    def calculate_overtime_penalty(resource, tasks):
        if resource is None or not tasks:
            return 0
        
        # Calculate total work time
        total_minutes = sum(task.duration_minutes for task in tasks)
        regular_hours_minutes = resource.max_daily_hours * 60
        
        if total_minutes > regular_hours_minutes:
            overtime_minutes = total_minutes - regular_hours_minutes
            return overtime_minutes // 15  # Penalty per 15-minute overtime block
        
        return 0
    
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.assigned_resource is not None)
            .group_by(lambda task: task.assigned_resource, ConstraintCollectors.to_list())
            .penalize(HardSoftScore.ONE_SOFT, calculate_overtime_penalty)
            .as_constraint('Minimize overtime costs'))


def prioritize_high_priority_tasks(cf):
    """
    Soft constraint: Ensure high-priority tasks are scheduled optimally
    """
    def calculate_priority_penalty(task):
        if task.assigned_resource is None:
            # Unassigned high-priority task gets heavy penalty
            return task.priority.value
        
        # Assigned tasks get small penalty based on priority for ordering
        return max(0, task.priority.value - Priority.ROUTINE.value)
    
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.priority.value > Priority.ROUTINE.value)
            .penalize(HardSoftScore.ONE_SOFT, calculate_priority_penalty)
            .as_constraint('Prioritize high priority tasks'))


def prefer_resource_preferences(cf):
    """
    Soft constraint: Consider resource preferences and patient preferences
    """
    def violates_preferences(task):
        if task.assigned_resource is None:
            return False
        
        # Check if this assignment violates any preferences
        # This could include patient preferences, resource preferences, etc.
        # For now, use resource priority as a simple preference indicator
        return task.assigned_resource.priority > 7  # Lower preference resources
    
    return (cf.for_each(HealthcareTask)
            .filter(violates_preferences)
            .penalize(HardSoftScore.ONE_SOFT)
            .as_constraint('Prefer resource preferences'))
