"""
Healthcare Constraint Provider - Medical Scheduling Constraints
==============================================================

This module provides healthcare-specific constraints that extend the core
constraint framework with medical compliance, safety protocols, and
regulatory requirements for home healthcare scheduling.

Key Constraints:
- Medical skill requirements and certifications
- Infection control and safety protocols
- Regulatory compliance (rest periods, licensing)
- Continuity of care preferences
- Emergency response prioritization
"""

import datetime
from typing import List, Optional

from timefold.solver.score import constraint_provider, ConstraintCollectors, Joiners
from timefold.solver.score import HardSoftScore

from core.constraints import ConstraintProvider
from core.domain import Priority
from .domain import HealthcareResource, HealthcareTask, InfectionControlLevel, HealthcareServiceTypes


@constraint_provider
class HealthcareConstraintProvider(ConstraintProvider):
    """
    Healthcare-specific constraint provider extending core constraints
    with medical compliance and safety requirements
    """
    
    def define_constraints(self, constraint_factory):
        """Define all healthcare scheduling constraints"""
        return [
            # Hard Constraints (Must be satisfied)
            *self.get_hard_constraints(constraint_factory),
            self.infection_control_violation(constraint_factory),
            self.regulatory_compliance_violation(constraint_factory),
            self.safety_pairing_violation(constraint_factory),
            self.supervision_requirement_violation(constraint_factory),
            
            # Soft Constraints (Optimization goals)
            *self.get_soft_constraints(constraint_factory),
            self.maximize_continuity_of_care(constraint_factory),
            self.minimize_travel_distance(constraint_factory),
            self.prioritize_emergency_tasks(constraint_factory),
            self.minimize_overtime_costs(constraint_factory),
        ]
    
    # ========================================================================
    # Healthcare-Specific Hard Constraints
    # ========================================================================
    
    def infection_control_violation(self, cf):
        """
        Hard constraint: Ensure proper infection control protocols
        """
        def violates_infection_control(task1, task2):
            if (task1.assigned_resource != task2.assigned_resource or
                task1.assigned_resource is None or
                task1.start_time is None or task2.start_time is None):
                return False
            
            # Check if tasks require isolation and are too close in time
            if (task1.infection_control_level == InfectionControlLevel.ISOLATION or
                task2.infection_control_level == InfectionControlLevel.ISOLATION):
                
                # Require minimum time between isolation tasks for decontamination
                min_decontamination_minutes = 60
                task1_end = task1.get_end_time()
                task2_end = task2.get_end_time()
                
                if task1_end and task2_end:
                    if task1_end < task2.start_time:
                        gap = (task2.start_time - task1_end).total_seconds() / 60
                        return gap < min_decontamination_minutes
                    elif task2_end < task1.start_time:
                        gap = (task1.start_time - task2_end).total_seconds() / 60
                        return gap < min_decontamination_minutes
            
            return False
        
        return (cf.for_each_unique_pair(HealthcareTask)
                .filter(violates_infection_control)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Infection control violation'))
    
    def regulatory_compliance_violation(self, cf):
        """
        Hard constraint: Ensure regulatory compliance (rest periods, certifications, etc.)
        """
        def violates_rest_period(task1, task2):
            if (task1.assigned_resource != task2.assigned_resource or
                task1.assigned_resource is None or
                task1.start_time is None or task2.start_time is None):
                return False
            
            # Check minimum rest period between tasks (30 minutes for healthcare)
            min_rest_minutes = 30
            task1_end = task1.get_end_time()
            
            if task1_end and task1_end < task2.start_time:
                rest_time = (task2.start_time - task1_end).total_seconds() / 60
                return rest_time < min_rest_minutes
            
            return False
        
        return (cf.for_each_unique_pair(HealthcareTask)
                .filter(violates_rest_period)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Regulatory compliance violation'))
    
    def safety_pairing_violation(self, cf):
        """
        Hard constraint: Require safety pairing in high-risk situations
        Based on TimeFold employee pairing patterns for home healthcare
        """
        def requires_safety_pairing(task):
            if task.assigned_resource is None or task.location is None:
                return False
            
            # High-risk conditions requiring pairing for home healthcare
            high_risk_conditions = [
                task.location.safety_rating <= 2,  # Unsafe neighborhood
                task.priority == Priority.CRITICAL,  # Emergency situation
                task.start_time and (task.start_time.hour < 6 or task.start_time.hour > 22),  # Night hours
                task.service_type == HealthcareServiceTypes.EMERGENCY_RESPONSE,  # Emergency calls
                task.infection_control_level == InfectionControlLevel.ISOLATION,  # Isolation cases
                "high_risk" in task.clinical_notes.lower() if task.clinical_notes else False
            ]
            
            if any(high_risk_conditions):
                # Check if resource has a paired colleague nearby
                return not self._has_safety_pair_available(task.assigned_resource, task.start_time, task.location)
            
            return False
        
        return (cf.for_each(HealthcareTask)
                .filter(requires_safety_pairing)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Safety pairing violation'))
    
    def supervision_requirement_violation(self, cf):
        """
        Hard constraint: Ensure required supervision is available
        """
        def lacks_required_supervision(task):
            if task.assigned_resource is None:
                return False
            
            # Check if resource requires supervision
            if task.assigned_resource.requires_supervision:
                # In real implementation, check for available supervisor
                # For now, simplified check
                return not task.assigned_resource.can_supervise  # Simplified
            
            return False
        
        return (cf.for_each(HealthcareTask)
                .filter(lacks_required_supervision)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Supervision requirement violation'))
    
    # ========================================================================
    # Healthcare-Specific Soft Constraints
    # ========================================================================
    
    def maximize_continuity_of_care(self, cf):
        """
        Soft constraint: Prefer assigning related patient tasks to the same clinician
        """
        def breaks_continuity(task1, task2):
            if (task1.assigned_resource == task2.assigned_resource or
                task1.patient_id != task2.patient_id or
                task1.patient_id == ""):
                return False
            
            # Same patient, different resources - breaks continuity
            # Higher penalty for services that especially benefit from continuity
            continuity_services = [
                HealthcareServiceTypes.WOUND_CARE,
                HealthcareServiceTypes.PHYSICAL_THERAPY,
                HealthcareServiceTypes.OCCUPATIONAL_THERAPY
            ]
            
            if (task1.service_type in continuity_services or 
                task2.service_type in continuity_services):
                return True
            
            return False
        
        return (cf.for_each_unique_pair(HealthcareTask)
                .filter(breaks_continuity)
                .penalize(HardSoftScore.of(0, 15))  # Higher penalty for continuity
                .as_constraint('Maximize continuity of care'))
    
    def minimize_travel_distance(self, cf):
        """
        Soft constraint: Minimize travel distance between patient homes
        """
        def calculate_travel_penalty(task1, task2):
            if (task1.assigned_resource != task2.assigned_resource or
                task1.location is None or task2.location is None):
                return 0
            
            # Calculate distance penalty (simplified - would use Google Maps in real implementation)
            if (task1.location.latitude and task1.location.longitude and
                task2.location.latitude and task2.location.longitude):
                
                distance = task1.location.distance_to(task2.location)
                if distance:
                    # Penalty increases with distance (fuel costs, time)
                    return int(distance * 2)  # 2 points per km
            
            return 5  # Default penalty for unknown distances
        
        return (cf.for_each_unique_pair(HealthcareTask)
                .filter(lambda t1, t2: t1.assigned_resource == t2.assigned_resource)
                .penalize(HardSoftScore.ONE_SOFT, calculate_travel_penalty)
                .as_constraint('Minimize travel distance'))
    
    def prioritize_emergency_tasks(self, cf):
        """
        Soft constraint: Prioritize emergency and high-priority tasks
        """
        def is_unassigned_emergency(task):
            return (task.assigned_resource is None and 
                    (task.priority == Priority.CRITICAL or 
                     task.service_type == HealthcareServiceTypes.EMERGENCY_RESPONSE))
        
        return (cf.for_each(HealthcareTask)
                .filter(is_unassigned_emergency)
                .penalize(HardSoftScore.of(0, 50))  # High penalty for unassigned emergencies
                .as_constraint('Prioritize emergency tasks'))
    
    def minimize_overtime_costs(self, cf):
        """
        Soft constraint: Minimize overtime costs by balancing workload
        """
        def calculate_overtime_penalty(resource, task_count):
            if resource is None:
                return 0
            
            # Calculate overtime penalty
            max_regular_tasks = 6  # Regular workload
            if task_count > max_regular_tasks:
                overtime_tasks = task_count - max_regular_tasks
                return overtime_tasks * overtime_tasks * 10  # Quadratic penalty
            
            return 0
        
        return (cf.for_each(HealthcareTask)
                .filter(lambda task: task.assigned_resource is not None)
                .group_by(lambda task: task.assigned_resource, ConstraintCollectors.count())
                .filter(lambda resource, count: count > 6)
                .penalize(HardSoftScore.ONE_SOFT, calculate_overtime_penalty)
                .as_constraint('Minimize overtime costs'))
    
    def prefer_specialized_skills(self, cf):
        """
        Soft constraint: Prefer using specialized skills for appropriate tasks
        """
        def skill_mismatch_penalty(task):
            if task.assigned_resource is None:
                return 0
            
            # Calculate penalty for skill over/under qualification
            task_complexity = task.get_clinical_complexity_score()
            
            # Count advanced skills of assigned resource
            advanced_skills = len([s for s in task.assigned_resource.skills 
                                 if s.level.value >= 3])  # Advanced or higher
            
            # Penalty for overqualification (waste of resources)
            if advanced_skills > 2 and task_complexity <= 3:
                return 5
            
            # Penalty for underqualification (quality concerns)
            if advanced_skills == 0 and task_complexity >= 7:
                return 10
            
            return 0
        
        return (cf.for_each(HealthcareTask)
                .penalize(HardSoftScore.ONE_SOFT, skill_mismatch_penalty)
                .as_constraint('Prefer specialized skills'))
    
    # ========================================================================
    # Helper Methods
    # ========================================================================
    
    def _has_safety_pair_available(self, resource: HealthcareResource, 
                                  time: datetime.datetime, 
                                  location) -> bool:
        """
        Helper function to check if safety pairing is available
        In production, this would check for other available resources
        """
        # Simplified implementation - in production this would:
        # 1. Find other resources available at the same time
        # 2. Check if they can be assigned to nearby locations
        # 3. Ensure proper pairing (e.g., male-female pairs for certain situations)
        return False  # For now, always require pairing for high-risk situations
    
    def _calculate_infection_control_gap_minutes(self, task1: HealthcareTask, 
                                               task2: HealthcareTask) -> int:
        """Calculate required gap between tasks for infection control"""
        # Base gap requirements by infection control level
        gap_requirements = {
            InfectionControlLevel.STANDARD: 15,
            InfectionControlLevel.CONTACT_PRECAUTIONS: 30,
            InfectionControlLevel.DROPLET_PRECAUTIONS: 30,
            InfectionControlLevel.AIRBORNE_PRECAUTIONS: 45,
            InfectionControlLevel.ISOLATION: 60
        }
        
        # Use the maximum requirement from both tasks
        gap1 = gap_requirements.get(task1.infection_control_level, 15)
        gap2 = gap_requirements.get(task2.infection_control_level, 15)
        
        return max(gap1, gap2)
