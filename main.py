import datetime
from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver import SolverFactory
from domain import Skill, ServiceArea, AvailabilitySlot, Clinician, Dependency, Visit, Schedule
import constraints  # noqa: F401  (registers via decorator)

def build_problem() -> Schedule:
    hou = ServiceArea('HOU')
    # time range every 15 minutes
    time_range = []
    t = datetime.datetime(2025, 6, 1, 8, 0)
    while t <= datetime.datetime(2025, 6, 1, 17, 45):
        time_range.append(t)
        t += datetime.timedelta(minutes=15)

    # clinicians
    clins = [
        Clinician(1, 'Alice', {Skill.SKILLED_NURSING}, {hou},
                  [AvailabilitySlot(datetime.datetime(2025,6,1,8,0),
                                    datetime.datetime(2025,6,1,18,0))]),
        Clinician(2, 'Bob', {Skill.SKILLED_NURSING}, {hou},
                  [AvailabilitySlot(datetime.datetime(2025,6,1,8,0),
                                    datetime.datetime(2025,6,1,18,0))])
    ]

    dep_ab = Dependency(1, False)
    dep_bc_same = Dependency(2, True)

    visits = [
        Visit(1, 'A', Skill.SKILLED_NURSING, hou,
              datetime.datetime(2025,6,1,9,0),
              datetime.datetime(2025,6,1,12,0),
              60, False, []),
        Visit(2, 'B', Skill.SKILLED_NURSING, hou,
              datetime.datetime(2025,6,1,10,0),
              datetime.datetime(2025,6,1,15,0),
              45, False, [dep_ab]),
        Visit(3, 'C', Skill.SKILLED_NURSING, hou,
              datetime.datetime(2025,6,1,11,0),
              datetime.datetime(2025,6,1,16,0),
              30, False, [dep_bc_same])
    ]

    return Schedule(clins, visits, time_range)

if __name__ == '__main__':
    schedule = build_problem()

    solver_config = SolverConfig(
        solution_class=Schedule,
        entity_class_list=[Visit],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=constraints.define_constraints
        ),
        termination_config=TerminationConfig(
            spent_limit=Duration(seconds=10)
        )
    )

    solver_factory = SolverFactory.create(solver_config)
    solver = solver_factory.build_solver()

    solved = solver.solve(schedule)
    print("Score:", solved.score)
    for v in sorted(solved.visits, key=lambda v: v.start):
        print(v.start, v.patient_name, "->", v.clinician)
