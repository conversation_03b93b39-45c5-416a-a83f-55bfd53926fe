"""
Production-Grade Domain-Agnostic Scheduling Framework
=====================================================

Main application demonstrating the healthcare scheduling implementation
of the domain-agnostic scheduling framework.

Architecture:
- Core Framework (80%): Reusable scheduling engine, constraints, and domain abstractions
- Domain Implementation (20%): Healthcare-specific extensions and integrations

Usage:
    python main.py                    # Run healthcare scheduling demo
    python main.py --domain education # Run education scheduling (future)
"""

import os
import sys
import argparse
import datetime
from typing import List

# Add project root to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.domain import TimeWindow, Location, LocationType
from core.solver import SchedulingRequest
from core.utils import TimeUtils, ValidationUtils

from domains.healthcare import (
    HealthcareResource, HealthcareTask, HealthcareSkills, HealthcareServiceTypes,
    HealthcareSchedulingEngine, InfectionControlLevel
)
from domains.healthcare.domain import Priority


def create_sample_healthcare_data():
    """Create sample data for healthcare scheduling demonstration"""
    
    # Create sample locations (patient homes and agency office)
    locations = [
        Location(
            id="agency_office",
            name="HomeCare Plus Agency Office",
            latitude=40.7128,
            longitude=-74.0060,
            location_type=LocationType.OFFICE,
            address="123 Main St, New York, NY 10001",
            timezone="America/New_York",
            safety_rating=5,
            accessibility_features=["wheelchair_accessible", "parking"],
            parking_available=True
        ),
        Location(
            id="patient_home_1",
            name="Johnson Family Home",
            latitude=40.7589,
            longitude=-73.9851,
            location_type=LocationType.CLIENT_SITE,
            address="456 Oak Ave, New York, NY 10002",
            timezone="America/New_York",
            safety_rating=4,
            accessibility_features=["ground_floor"],
            parking_available=True
        ),
        Location(
            id="patient_home_2",
            name="Smith Family Home", 
            latitude=40.7282,
            longitude=-73.7949,
            location_type=LocationType.CLIENT_SITE,
            address="789 Pine St, Queens, NY 11375",
            timezone="America/New_York",
            safety_rating=3,
            accessibility_features=[],
            parking_available=False
        ),
        Location(
            id="patient_home_3",
            name="Williams Family Home",
            latitude=40.7831,
            longitude=-73.9712,
            location_type=LocationType.CLIENT_SITE,
            address="321 Elm Dr, Bronx, NY 10025",
            timezone="America/New_York",
            safety_rating=2,  # Lower safety area requiring pairing
            accessibility_features=["wheelchair_accessible"],
            parking_available=False
        )
    ]
    
    # Create sample healthcare resources (clinicians)
    agency_office = locations[0]
    
    morning_shift = TimeUtils.create_daily_availability(8, 16, timezone="America/New_York")
    afternoon_shift = TimeUtils.create_daily_availability(12, 20, timezone="America/New_York")
    
    resources = [
        HealthcareResource(
            id="nurse_alice",
            name="Alice Johnson, RN",
            skills=[
                HealthcareSkills.WOUND_CARE_ADVANCED,
                HealthcareSkills.MEDICATION_IV,
                HealthcareSkills.MEDICATION_BASIC,
                HealthcareSkills.HEALTH_ASSESSMENT
            ],
            availability_windows=[morning_shift],
            location=agency_office,
            priority=3,
            license_number="RN123456",
            certifications=["IV_CERTIFIED", "WOUND_CARE_SPECIALIST"],
            specializations=["DIABETES_CARE", "WOUND_MANAGEMENT"],
            max_daily_hours=8,
            max_weekly_hours=40,
            can_supervise=True
        ),
        HealthcareResource(
            id="nurse_bob",
            name="Bob Smith, LPN",
            skills=[
                HealthcareSkills.WOUND_CARE_BASIC,
                HealthcareSkills.MEDICATION_BASIC
            ],
            availability_windows=[afternoon_shift],
            location=agency_office,
            priority=5,
            license_number="LPN789012",
            certifications=["BASIC_LIFE_SUPPORT"],
            max_daily_hours=8,
            max_weekly_hours=40,
            requires_supervision=True
        ),
        HealthcareResource(
            id="therapist_carol",
            name="Carol Davis, PT",
            skills=[HealthcareSkills.PHYSICAL_THERAPY],
            availability_windows=[morning_shift],
            location=agency_office,
            priority=2,
            license_number="PT345678",
            certifications=["PHYSICAL_THERAPY_LICENSE"],
            specializations=["ORTHOPEDIC_REHAB", "GERIATRIC_CARE"],
            max_daily_hours=8,
            max_weekly_hours=40
        )
    ]
    
    # Create sample healthcare tasks (patient visits)
    patient_home_1 = locations[1]
    patient_home_2 = locations[2]
    patient_home_3 = locations[3]  # Unsafe area
    
    morning_window = TimeUtils.create_time_window(9, 0, 12, 0, timezone="America/New_York")
    afternoon_window = TimeUtils.create_time_window(13, 0, 17, 0, timezone="America/New_York")
    emergency_window = TimeUtils.create_time_window(8, 0, 20, 0, timezone="America/New_York")
    
    tasks = [
        HealthcareTask(
            id="visit_wound_care_001",
            name="Advanced Wound Care - Johnson",
            duration_minutes=90,
            required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
            time_windows=[morning_window],
            location=patient_home_1,
            priority=Priority.HIGH,
            patient_id="patient_johnson",
            service_type=HealthcareServiceTypes.WOUND_CARE,
            clinical_notes="Complex diabetic ulcer requiring advanced wound care",
            equipment_required=["wound_care_kit", "sterile_supplies"],
            infection_control_level=InfectionControlLevel.CONTACT_PRECAUTIONS
        ),
        HealthcareTask(
            id="visit_medication_001",
            name="IV Medication Administration - Johnson",
            duration_minutes=45,
            required_skills=[HealthcareSkills.MEDICATION_IV],
            time_windows=[afternoon_window],
            location=patient_home_1,
            priority=Priority.ROUTINE,
            patient_id="patient_johnson",
            service_type=HealthcareServiceTypes.MEDICATION_ADMIN,
            clinical_notes="Daily IV antibiotic administration",
            equipment_required=["IV_supplies", "medication"],
            continuity_requirements=["same_clinician_as_wound_care"],
            dependencies=["visit_wound_care_001"]
        ),
        HealthcareTask(
            id="visit_basic_care_001",
            name="Basic Wound Care - Smith",
            duration_minutes=60,
            required_skills=[HealthcareSkills.WOUND_CARE_BASIC],
            time_windows=[morning_window, afternoon_window],
            location=patient_home_2,
            priority=Priority.ROUTINE,
            patient_id="patient_smith",
            service_type=HealthcareServiceTypes.WOUND_CARE,
            clinical_notes="Simple dressing change",
            equipment_required=["basic_wound_supplies"]
        ),
        HealthcareTask(
            id="visit_emergency_001",
            name="Emergency Home Visit - Williams",
            duration_minutes=120,
            required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED, HealthcareSkills.EMERGENCY_RESPONSE],
            time_windows=[emergency_window],
            location=patient_home_3,  # Unsafe area requiring safety pairing
            priority=Priority.CRITICAL,
            patient_id="patient_williams",
            service_type=HealthcareServiceTypes.EMERGENCY_RESPONSE,
            clinical_notes="Emergency wound dehiscence requiring immediate home visit",
            equipment_required=["emergency_kit", "IV_supplies", "advanced_wound_care"]
        ),
        HealthcareTask(
            id="visit_therapy_001",
            name="Physical Therapy - Rehabilitation",
            duration_minutes=75,
            required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
            time_windows=[morning_window],
            location=patient_home_1,
            priority=Priority.ROUTINE,
            patient_id="patient_johnson",
            service_type=HealthcareServiceTypes.PHYSICAL_THERAPY,
            clinical_notes="Post-surgical mobility rehabilitation",
            equipment_required=["therapy_equipment"]
        )
    ]
    
    return locations, resources, tasks


def validate_sample_data(locations: List[Location], resources: List[HealthcareResource], 
                        tasks: List[HealthcareTask]):
    """Validate sample data using framework validation utilities"""
    print("🔍 Validating sample data...")
    
    errors = ValidationUtils.validate_schedule_request(resources, tasks, locations)
    
    if errors:
        print("❌ Validation errors found:")
        for error in errors:
            print(f"  • {error}")
        return False
    else:
        print("✅ All data validation passed")
        return True


def run_healthcare_scheduling_demo():
    """Run the healthcare scheduling demonstration"""
    
    print("🏥 Production-Grade Healthcare Scheduling Framework")
    print("=" * 60)
    print("Domain-Agnostic Core + Healthcare Specialization")
    print()
    
    # Create sample data
    print("📋 Creating sample healthcare data...")
    locations, resources, tasks = create_sample_healthcare_data()
    
    # Validate data
    if not validate_sample_data(locations, resources, tasks):
        print("❌ Data validation failed. Exiting.")
        return
    
    print(f"📍 Locations: {len(locations)}")
    print(f"👥 Resources: {len(resources)}")
    print(f"📋 Tasks: {len(tasks)}")
    print()
    
    # Initialize healthcare scheduling engine
    google_api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4')
    scheduler = HealthcareSchedulingEngine(google_api_key)
    
    # Create scheduling request
    request = SchedulingRequest(
        resources=resources,
        tasks=tasks,
        locations=locations,
        schedule_date=datetime.date.today(),
        optimization_time_seconds=30,
        include_travel_optimization=True
    )
    
    # Generate optimized schedule
    print("🔄 Optimizing healthcare schedule...")
    print("   • Applying medical constraints")
    print("   • Optimizing travel routes")
    print("   • Ensuring regulatory compliance")
    print()
    
    result = scheduler.create_schedule(request)
    
    # Display results
    print(f"✅ Optimization completed in {result.optimization_time_seconds:.2f} seconds")
    print(f"📊 Score: {result.score}")
    print(f"🎯 Feasible: {'Yes' if result.is_feasible() else 'No'}")
    print()
    
    # Display schedule
    print("📅 OPTIMIZED HEALTHCARE SCHEDULE")
    print("-" * 45)
    
    for resource in resources:
        resource_tasks = [t for t in result.schedule.tasks if t.assigned_resource == resource]
        if resource_tasks:
            print(f"\n👤 {resource.name}")
            sorted_tasks = sorted(resource_tasks, key=lambda t: t.start_time or datetime.datetime.min)
            for task in sorted_tasks:
                if task.start_time and task.location:
                    end_time = task.get_end_time()
                    if end_time:
                        print(f"  {task.start_time.strftime('%H:%M')}-{end_time.strftime('%H:%M')} | "
                              f"{task.name} | {task.location.name}")
                        if task.clinical_notes:
                            print(f"    📝 {task.clinical_notes[:50]}...")
    
    # Display unassigned tasks
    unassigned = result.unassigned_tasks
    if unassigned:
        print(f"\n⚠️  UNASSIGNED TASKS ({len(unassigned)})")
        for task in unassigned:
            print(f"  • {task.name} (Priority: {task.priority.name})")
    
    # Display statistics
    print(f"\n📈 HEALTHCARE STATISTICS")
    print("-" * 25)
    stats = result.statistics
    print(f"Assignment Rate: {stats.get('assignment_rate', 0):.1f}%")
    print(f"Emergency Assignment Rate: {stats.get('emergency_assignment_rate', 0):.1f}%")
    print(f"Continuity Score: {stats.get('continuity_score', 0):.1f}%")
    print(f"Specialization Match Rate: {stats.get('specialization_match_rate', 0):.1f}%")
    
    # Display travel summary
    if result.travel_summary:
        print(f"\n🚗 TRAVEL OPTIMIZATION")
        print("-" * 22)
        travel = result.travel_summary
        print(f"Total Distance: {travel.get('total_distance_km', 0):.1f} km")
        print(f"Total Travel Time: {travel.get('total_duration_hours', 0):.1f} hours")
        print(f"Estimated Fuel Cost: ${travel.get('estimated_total_fuel_cost', 0):.2f}")
        print(f"Travel Efficiency: {travel.get('travel_efficiency_score', 0):.1f}/100")
    
    # Display recommendations
    if result.recommendations:
        print(f"\n💡 RECOMMENDATIONS")
        print("-" * 18)
        for i, rec in enumerate(result.recommendations, 1):
            print(f"{i}. {rec}")
    
    print(f"\n🎯 Healthcare scheduling framework demonstration completed!")
    print("   • Core framework (80%) provides reusable scheduling engine")
    print("   • Healthcare domain (20%) adds medical constraints and integrations")
    print("   • Ready for production use by home healthcare agencies")


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description="Domain-Agnostic Scheduling Framework")
    parser.add_argument("--domain", default="healthcare", 
                       choices=["healthcare"], 
                       help="Domain to demonstrate (currently only healthcare)")
    
    args = parser.parse_args()
    
    if args.domain == "healthcare":
        run_healthcare_scheduling_demo()
    else:
        print(f"Domain '{args.domain}' not yet implemented")
        print("Available domains: healthcare")


if __name__ == "__main__":
    main()
