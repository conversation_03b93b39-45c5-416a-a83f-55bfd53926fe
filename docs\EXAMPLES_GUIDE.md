# Practical Examples Guide

## Common Scheduling Scenarios and Solutions

This guide provides practical examples of how to implement common healthcare scheduling requirements.

## 1. Multi-Day Scheduling

### Problem: Schedule visits across multiple days

```python
def build_multi_day_problem():
    # Generate time slots for 3 days
    time_range = []
    for day in range(3):  # 3 days
        base_date = datetime.datetime(2025, 6, 1 + day, 8, 0)
        for hour in range(10):  # 8 AM to 6 PM
            for minute in [0, 15, 30, 45]:
                time_range.append(base_date + datetime.timedelta(hours=hour, minutes=minute))
    
    # Clinician with different availability each day
    alice_availability = [
        AvailabilitySlot(datetime.datetime(2025, 6, 1, 8, 0), datetime.datetime(2025, 6, 1, 18, 0)),  # Monday
        AvailabilitySlot(datetime.datetime(2025, 6, 2, 9, 0), datetime.datetime(2025, 6, 2, 17, 0)),  # Tuesday
        AvailabilitySlot(datetime.datetime(2025, 6, 3, 8, 0), datetime.datetime(2025, 6, 3, 16, 0))   # Wednesday
    ]
    
    clinicians = [
        Clinician(1, 'Alice', {Skill.SKILLED_NURSING}, {hou}, alice_availability)
    ]
    
    # Visits with different day preferences
    visits = [
        Visit(1, 'Patient A', Skill.SKILLED_NURSING, hou,
              datetime.datetime(2025, 6, 1, 9, 0),   # Monday window
              datetime.datetime(2025, 6, 1, 17, 0),
              60, False, []),
        Visit(2, 'Patient B', Skill.SKILLED_NURSING, hou,
              datetime.datetime(2025, 6, 2, 10, 0),  # Tuesday window
              datetime.datetime(2025, 6, 3, 16, 0),  # Can extend to Wednesday
              45, False, [])
    ]
    
    return Schedule(clinicians, visits, time_range)
```

## 2. Priority-Based Scheduling

### Problem: Some visits are more urgent than others

```python
@dataclass
class PriorityVisit(Visit):
    priority: int = 1  # 1=low, 2=medium, 3=high, 4=urgent

def priority_constraint(cf):
    """Prefer scheduling high-priority visits earlier in the day"""
    return (cf.for_each(PriorityVisit)
            .filter(lambda v: v.start is not None)
            .reward(HardSoftScore.ONE_SOFT,
                    lambda v: v.priority * (18 - v.start.hour))  # Earlier = better for high priority
            .as_constraint('Priority scheduling'))

def urgent_visit_constraint(cf):
    """Urgent visits must be scheduled within 2 hours of window start"""
    return (cf.for_each(PriorityVisit)
            .filter(lambda v: v.priority == 4 and v.start is not None and
                    v.start > v.window_start + datetime.timedelta(hours=2))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Urgent visit delay'))
```

## 3. Skill Specialization and Training

### Problem: Clinicians have different skill levels and training requirements

```python
@dataclass
class SkillLevel:
    skill: str
    level: int  # 1=basic, 2=intermediate, 3=advanced, 4=expert

@dataclass
class AdvancedClinician(Clinician):
    skill_levels: Dict[str, int] = field(default_factory=dict)
    training_hours_needed: int = 0

@dataclass
class ComplexVisit(Visit):
    required_skill_level: int = 1
    provides_training_hours: int = 0

def skill_level_constraint(cf):
    """Clinician skill level must meet or exceed visit requirements"""
    return (cf.for_each(ComplexVisit)
            .filter(lambda v: v.clinician is not None and
                    v.clinician.skill_levels.get(v.required_skill, 0) < v.required_skill_level)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Insufficient skill level'))

def training_opportunity_constraint(cf):
    """Reward visits that provide training hours to clinicians who need them"""
    return (cf.for_each(ComplexVisit)
            .filter(lambda v: v.clinician is not None and
                    v.provides_training_hours > 0 and
                    v.clinician.training_hours_needed > 0)
            .reward(HardSoftScore.ONE_SOFT,
                    lambda v: min(v.provides_training_hours, v.clinician.training_hours_needed))
            .as_constraint('Training opportunity'))
```

## 4. Geographic Optimization

### Problem: Minimize travel time between visits

```python
@dataclass
class Location:
    latitude: float
    longitude: float
    
    def distance_to(self, other: 'Location') -> float:
        """Calculate distance in miles (simplified)"""
        lat_diff = abs(self.latitude - other.latitude)
        lon_diff = abs(self.longitude - other.longitude)
        return ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 69  # Rough miles conversion

@dataclass
class GeoVisit(Visit):
    location: Location

def travel_time_constraint(cf):
    """Penalize long travel distances between consecutive visits"""
    def same_clinician_different_locations(v1, v2):
        return (v1.clinician == v2.clinician and 
                v1.clinician is not None and
                v1.start is not None and v2.start is not None and
                v1.location != v2.location)
    
    def calculate_travel_penalty(v1, v2):
        # Determine which visit comes first
        if v1.start <= v2.start:
            first, second = v1, v2
        else:
            first, second = v2, v1
        
        # Check if there's enough time between visits
        first_end = first.start + datetime.timedelta(minutes=first.duration_minutes)
        travel_time_needed = first.location.distance_to(second.location) * 2  # 2 minutes per mile
        
        if second.start < first_end + datetime.timedelta(minutes=travel_time_needed):
            return int(travel_time_needed)  # Penalty = minutes of travel time violation
        return 0
    
    return (cf.for_each_unique_pair(GeoVisit)
            .filter(same_clinician_different_locations)
            .penalize(HardSoftScore.ONE_SOFT, calculate_travel_penalty)
            .as_constraint('Travel time optimization'))
```

## 5. Equipment and Resource Management

### Problem: Some visits require special equipment with limited availability

```python
@dataclass
class Equipment:
    id: int
    name: str
    available_count: int
    service_areas: Set[ServiceArea]

@dataclass
class EquipmentVisit(Visit):
    required_equipment: Optional[Equipment] = None

def equipment_availability_constraint(cf):
    """Ensure equipment is available in the service area"""
    return (cf.for_each(EquipmentVisit)
            .filter(lambda v: v.required_equipment is not None and
                    v.service_area not in v.required_equipment.service_areas)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Equipment not available in area'))

def equipment_capacity_constraint(cf):
    """Don't exceed equipment capacity at any time"""
    def overlapping_equipment_usage(v1, v2):
        if (v1.required_equipment != v2.required_equipment or 
            v1.required_equipment is None or
            v1.start is None or v2.start is None):
            return False
        
        # Check if visits overlap in time
        v1_end = v1.start + datetime.timedelta(minutes=v1.duration_minutes)
        v2_end = v2.start + datetime.timedelta(minutes=v2.duration_minutes)
        
        return not (v1_end <= v2.start or v2_end <= v1.start)
    
    return (cf.for_each(EquipmentVisit)
            .join(EquipmentVisit, Joiners.filtering(overlapping_equipment_usage))
            .group_by(lambda v1, v2: v1.required_equipment,
                      ConstraintCollectors.count())
            .filter(lambda equipment, count: equipment is not None and count > equipment.available_count)
            .penalize(HardSoftScore.ONE_HARD,
                      lambda equipment, count: count - equipment.available_count)
            .as_constraint('Equipment capacity exceeded'))
```

## 6. Continuity of Care

### Problem: Patients prefer to see the same clinician for follow-up visits

```python
@dataclass
class ContinuityVisit(Visit):
    patient_id: int
    visit_sequence: int  # 1st visit, 2nd visit, etc.
    preferred_clinician: Optional[Clinician] = None

def continuity_of_care_constraint(cf):
    """Reward assigning follow-up visits to the same clinician"""
    def is_follow_up_pair(v1, v2):
        return (v1.patient_id == v2.patient_id and 
                v1.visit_sequence == v2.visit_sequence - 1)
    
    return (cf.for_each(ContinuityVisit)
            .join(ContinuityVisit, Joiners.filtering(is_follow_up_pair))
            .filter(lambda v1, v2: v1.clinician is not None and 
                    v2.clinician is not None and
                    v1.clinician == v2.clinician)
            .reward(HardSoftScore.ONE_SOFT)
            .as_constraint('Continuity of care'))

def preferred_clinician_constraint(cf):
    """Reward assigning visits to patient's preferred clinician"""
    return (cf.for_each(ContinuityVisit)
            .filter(lambda v: v.preferred_clinician is not None and
                    v.clinician == v.preferred_clinician)
            .reward(HardSoftScore.ONE_SOFT)
            .as_constraint('Preferred clinician'))
```

## 7. Workload Balancing

### Problem: Distribute work evenly among clinicians

```python
def workload_balance_constraint(cf):
    """Minimize the difference in total work hours between clinicians"""
    # Calculate total minutes for each clinician
    clinician_workloads = (cf.for_each(Visit)
                          .group_by(lambda v: v.clinician,
                                    ConstraintCollectors.sum(lambda v: v.duration_minutes if v.clinician else 0)))
    
    # Penalize deviations from average workload
    return (clinician_workloads
            .filter(lambda c, minutes: c is not None)
            .penalize(HardSoftScore.ONE_SOFT,
                      lambda c, minutes: abs(minutes - 240))  # Target 4 hours (240 minutes)
            .as_constraint('Workload balance'))

def fair_distribution_constraint(cf):
    """Ensure no clinician is overloaded while others are underutilized"""
    return (cf.for_each(Visit)
            .join(Visit, Joiners.filtering(lambda v1, v2: v1.clinician != v2.clinician and
                                          v1.clinician is not None and v2.clinician is not None))
            .group_by(lambda v1, v2: (v1.clinician, v2.clinician),
                      ConstraintCollectors.sum(lambda v1, v2: v1.duration_minutes),
                      ConstraintCollectors.sum(lambda v1, v2: v2.duration_minutes))
            .filter(lambda pair, minutes1, minutes2: abs(minutes1 - minutes2) > 120)  # More than 2 hours difference
            .penalize(HardSoftScore.ONE_SOFT,
                      lambda pair, minutes1, minutes2: abs(minutes1 - minutes2) // 60)  # Penalty per hour difference
            .as_constraint('Fair distribution'))
```

## 8. Emergency and On-Call Scheduling

### Problem: Handle emergency visits and on-call requirements

```python
@dataclass
class EmergencyVisit(Visit):
    is_emergency: bool = False
    max_response_time_minutes: int = 60

@dataclass
class OnCallClinician(Clinician):
    on_call_periods: List[AvailabilitySlot] = field(default_factory=list)

def emergency_response_constraint(cf):
    """Emergency visits must be scheduled within response time"""
    return (cf.for_each(EmergencyVisit)
            .filter(lambda v: v.is_emergency and v.start is not None and
                    v.start > v.window_start + datetime.timedelta(minutes=v.max_response_time_minutes))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Emergency response time'))

def on_call_availability_constraint(cf):
    """Emergency visits should prefer on-call clinicians"""
    def is_on_call(visit):
        if not isinstance(visit.clinician, OnCallClinician) or visit.start is None:
            return False
        
        for period in visit.clinician.on_call_periods:
            if period.contains_range(visit.start, 
                                   visit.start + datetime.timedelta(minutes=visit.duration_minutes)):
                return True
        return False
    
    return (cf.for_each(EmergencyVisit)
            .filter(lambda v: v.is_emergency and v.clinician is not None)
            .reward(HardSoftScore.ONE_SOFT,
                    lambda v: 10 if is_on_call(v) else 0)
            .as_constraint('On-call preference'))
```

## 9. Running Complex Examples

### Complete Example Setup
```python
def build_complex_problem():
    # Service areas
    downtown = ServiceArea('DOWNTOWN')
    suburbs = ServiceArea('SUBURBS')
    
    # Equipment
    wheelchair = Equipment(1, 'Wheelchair', 2, {downtown, suburbs})
    oxygen = Equipment(2, 'Oxygen Tank', 1, {downtown})
    
    # Locations
    hospital = Location(40.7128, -74.0060)
    clinic = Location(40.7589, -73.9851)
    
    # Advanced clinicians
    alice = AdvancedClinician(
        1, 'Alice', {Skill.SKILLED_NURSING}, {downtown, suburbs},
        [AvailabilitySlot(datetime.datetime(2025, 6, 1, 8, 0), datetime.datetime(2025, 6, 1, 18, 0))],
        5, {Skill.SKILLED_NURSING: 3}, 4
    )
    
    # Complex visits
    visits = [
        ComplexVisit(1, 'Patient A', Skill.SKILLED_NURSING, downtown,
                    datetime.datetime(2025, 6, 1, 9, 0), datetime.datetime(2025, 6, 1, 12, 0),
                    60, False, [], None, None, 2, 2, hospital, wheelchair, 1, None),
        # Add more complex visits...
    ]
    
    return Schedule([alice], visits, generate_time_range())

# Run with all constraints
def solve_complex_problem():
    problem = build_complex_problem()
    
    # Add all constraint types
    all_constraints = [
        skill_level_constraint,
        equipment_availability_constraint,
        travel_time_constraint,
        workload_balance_constraint,
        emergency_response_constraint,
        # ... other constraints
    ]
    
    solver_config = SolverConfig(
        solution_class=Schedule,
        entity_class_list=[ComplexVisit],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=lambda cf: [c(cf) for c in all_constraints]
        ),
        termination_config=TerminationConfig(spent_limit=Duration(seconds=30))
    )
    
    solver = SolverFactory.create(solver_config).build_solver()
    return solver.solve(problem)
```

These examples demonstrate how to handle real-world complexity in healthcare scheduling systems using TimeFold's flexible constraint system.
