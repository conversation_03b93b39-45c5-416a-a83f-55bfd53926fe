# Architecture Overview

## System Architecture

The Domain-Agnostic Healthcare Scheduling Framework follows a layered architecture pattern inspired by TimeFold Solver's design principles, adapted for healthcare-specific requirements.

```
┌─────────────────────────────────────────────────────────────────────┐
│                        Presentation Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │   Web API       │  │   CLI Interface │  │  Jupyter Demos │     │
│  │ (healthcare_    │  │ (healthcare_    │  │   (examples/)   │     │
│  │  main.py)       │  │  main.py)       │  │                 │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
└─────────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────────┐
│                      Application Layer                              │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │              Healthcare Scheduling Engine                   │   │
│  │                (healthcare_scheduler.py)                    │   │
│  │                                                             │   │
│  │  • Schedule Creation & Optimization                         │   │
│  │  • Real-Time Updates & Adjustments                          │   │
│  │  • Recommendation Generation                                │   │
│  │  • Statistics & Analytics                                   │   │
│  │  • Configuration Management                                 │   │
│  └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────────┐
│                        Domain Layer                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │   Domain Model  │  │   Constraints   │  │  Configuration  │     │
│  │ (healthcare_    │  │ (healthcare_    │  │   (healthcare_  │     │
│  │  domain.py)     │  │ constraints.py) │  │   domain.py)    │     │
│  │                 │  │                 │  │                 │     │
│  │ • Resources     │  │ • Hard Rules    │  │ • Weights       │     │
│  │ • Tasks         │  │ • Soft Goals    │  │ • Scenarios     │     │
│  │ • Locations     │  │ • Healthcare    │  │ • Customization │     │
│  │ • Skills        │  │   Specific      │  │                 │     │
│  │ • Time Windows  │  │                 │  │                 │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
└─────────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │  TimeFold       │  │  Google Maps    │  │  External APIs  │     │
│  │  Solver         │  │  Integration    │  │  & Services     │     │
│  │                 │  │ (google_maps_   │  │                 │     │
│  │ • Optimization  │  │  service.py)    │  │ • EHR Systems   │     │
│  │ • Constraints   │  │                 │  │ • Scheduling    │     │
│  │ • Scoring       │  │ • Route Calc    │  │   Systems       │     │
│  │ • Search        │  │ • Travel Times  │  │ • Notification  │     │
│  │                 │  │ • Real-time     │  │   Services      │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
└─────────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Healthcare Scheduling Engine (`healthcare_scheduler.py`)

**Purpose**: Main orchestration component that coordinates all aspects of healthcare scheduling.

**Key Responsibilities**:
- Schedule creation and optimization
- Real-time schedule updates
- Recommendation generation
- Performance analytics
- Configuration management

**Design Patterns**:
- **Facade Pattern**: Provides simplified interface to complex subsystems
- **Strategy Pattern**: Configurable optimization strategies for different healthcare scenarios
- **Observer Pattern**: Real-time updates and notifications

```python
class HealthcareSchedulingEngine:
    def __init__(self, google_maps_api_key: str = None):
        self.google_maps_service = GoogleMapsHealthcareService(google_maps_api_key)
        self.solver_factory = self._initialize_solver()
    
    def create_schedule(self, request: SchedulingRequest) -> SchedulingResult:
        # Main scheduling workflow
        pass
    
    def update_schedule_real_time(self, schedule, changes) -> SchedulingResult:
        # Real-time optimization
        pass
```

### 2. Domain Model (`healthcare_domain.py`)

**Purpose**: Defines the core abstractions and healthcare-specific entities using domain-driven design principles.

**Key Components**:

#### Abstract Base Classes (Domain-Agnostic)
```python
@dataclass
class Resource(ABC):
    """Abstract base for all schedulable resources"""
    id: str
    name: str
    skills: List[Skill]
    availability_windows: List[TimeWindow]
    
    @abstractmethod
    def can_perform_skill(self, required_skill: Skill) -> bool:
        pass

@dataclass  
class Task(ABC):
    """Abstract base for all schedulable tasks"""
    id: str
    name: str
    duration_minutes: int
    required_skills: List[Skill]
    
    @abstractmethod
    def get_end_time(self) -> Optional[datetime.datetime]:
        pass
```

#### Healthcare-Specific Implementations
```python
@dataclass
class HealthcareResource(Resource):
    """Healthcare-specific resource with medical attributes"""
    license_number: Optional[str] = None
    certifications: List[str] = field(default_factory=list)
    specializations: List[str] = field(default_factory=list)
    max_daily_hours: int = 8
    requires_supervision: bool = False
    can_supervise: bool = False

@planning_entity
@dataclass
class HealthcareTask(Task):
    """Healthcare-specific task with medical context"""
    patient_id: str = ""
    service_type: str = ""
    clinical_notes: str = ""
    infection_control_level: str = "STANDARD"
    dependencies: List[str] = field(default_factory=list)
    
    # TimeFold Planning Variables
    assigned_resource: Annotated[Optional[HealthcareResource], 
                                PlanningVariable(...)] = field(default=None)
    start_time: Annotated[Optional[datetime.datetime], 
                         PlanningVariable(...)] = field(default=None)
```

### 3. Constraint System (`healthcare_constraints.py`)

**Purpose**: Implements healthcare-specific business rules and optimization goals using TimeFold's constraint stream API.

**Architecture**:
- **Hard Constraints**: Must be satisfied (feasibility)
- **Soft Constraints**: Optimization goals (quality)
- **Configurable Weights**: Adapt to different scenarios

```python
@constraint_provider
def define_healthcare_constraints(constraint_factory):
    return [
        # Hard Constraints (Feasibility)
        skill_requirement_violation(constraint_factory),
        resource_capacity_exceeded(constraint_factory),
        time_window_violation(constraint_factory),
        resource_double_booking(constraint_factory),
        
        # Soft Constraints (Optimization)
        minimize_travel_distance(constraint_factory),
        balance_workload_distribution(constraint_factory),
        maximize_continuity_of_care(constraint_factory),
        prefer_skill_exact_match(constraint_factory)
    ]
```

**Constraint Categories**:

1. **Resource Management**
   - Skill matching with hierarchy support
   - Capacity and availability limits
   - Certification and licensing requirements

2. **Temporal Constraints**
   - Time window compliance
   - Task dependencies and sequencing
   - Regulatory rest periods

3. **Spatial Constraints**
   - Location accessibility
   - Travel time optimization
   - Safety area restrictions

4. **Healthcare-Specific Rules**
   - Continuity of care preferences
   - Infection control protocols
   - Emergency response priorities

### 4. Google Maps Integration (`google_maps_service.py`)

**Purpose**: Provides real-world routing and travel optimization for healthcare scheduling.

**Key Features**:
- Real-time travel data with traffic awareness
- Multi-stop route optimization
- Healthcare-specific routing considerations
- Caching for performance optimization

```python
class GoogleMapsHealthcareService:
    def get_travel_info(self, origin: Location, destination: Location) -> TravelInfo:
        # Real-time travel calculation
        pass
    
    def optimize_healthcare_route(self, visits: List[Tuple[Location, datetime]]) -> RouteOptimization:
        # Multi-stop route optimization
        pass
    
    def calculate_healthcare_constraints(self, tasks, resources) -> Dict[str, any]:
        # Generate travel-based constraints for TimeFold
        pass
```

## Data Flow

### 1. Schedule Creation Flow

```
Input Data → Domain Model → Constraint Evaluation → Optimization → Result
     ↓              ↓              ↓                    ↓           ↓
Resources      Healthcare     Hard/Soft           TimeFold    Optimized
Tasks          Entities       Constraints         Solver      Schedule
Locations      Skills         Rules               Engine      Statistics
Config         TimeWindows    Weights                         Travel Data
```

### 2. Real-Time Update Flow

```
Change Event → Impact Analysis → Incremental Optimization → Updated Schedule
     ↓               ↓                    ↓                      ↓
Emergency      Affected Tasks      Limited Re-optimization    Notifications
Staff Change   Constraint Check    Quick Solver Run          Updated Routes
Traffic        Feasibility         Minimal Disruption        New Assignments
```

### 3. Recommendation Flow

```
New Task → Candidate Analysis → Scoring → Ranked Recommendations
    ↓            ↓                ↓              ↓
Request     Available         Impact        Best Options
Context     Resources         Assessment    with Rationale
Skills      Time Slots        Travel Cost   Confidence Score
Priority    Locations         Disruption    Alternative Plans
```

## Design Principles

### 1. Domain-Agnostic Core with Healthcare Specialization

**Pattern**: Abstract base classes provide domain-agnostic scheduling capabilities, while concrete implementations add healthcare-specific features.

**Benefits**:
- Reusable core for other domains (education, field service, etc.)
- Healthcare-specific optimizations without losing generality
- Easy extension for new healthcare scenarios

### 2. Constraint-Driven Architecture

**Pattern**: Business rules are expressed as constraints that can be configured and weighted for different scenarios.

**Benefits**:
- Flexible adaptation to different healthcare environments
- Clear separation of business logic from optimization engine
- Easy addition of new rules without code changes

### 3. Layered Integration

**Pattern**: External services (Google Maps, TimeFold) are abstracted through service layers.

**Benefits**:
- Testable without external dependencies
- Swappable implementations (different mapping services)
- Graceful degradation when services unavailable

### 4. Event-Driven Real-Time Updates

**Pattern**: Schedule changes trigger events that are processed incrementally.

**Benefits**:
- Fast response to urgent changes
- Minimal disruption to existing schedules
- Scalable to large healthcare operations

## Scalability Considerations

### 1. Horizontal Scaling
- **Microservice Architecture**: Each component can be deployed independently
- **Event Streaming**: Use message queues for real-time updates
- **Caching Layers**: Redis for travel data and constraint calculations

### 2. Performance Optimization
- **Constraint Filtering**: Early filtering to reduce search space
- **Incremental Solving**: Only re-optimize affected parts of schedule
- **Parallel Processing**: Multiple solver instances for different regions

### 3. Data Management
- **Time-Series Storage**: Historical scheduling data for analytics
- **Graph Databases**: Relationship modeling for complex dependencies
- **Distributed Caching**: Shared cache across multiple instances

## Security & Compliance

### 1. Healthcare Data Protection
- **HIPAA Compliance**: Patient data encryption and access controls
- **Audit Logging**: Complete trail of scheduling decisions
- **Data Minimization**: Only store necessary information

### 2. API Security
- **Authentication**: OAuth 2.0 for API access
- **Authorization**: Role-based access control
- **Rate Limiting**: Prevent abuse of optimization services

### 3. Integration Security
- **API Key Management**: Secure storage of Google Maps keys
- **Network Security**: TLS encryption for all communications
- **Input Validation**: Sanitization of all external inputs

This architecture provides a robust, scalable foundation for healthcare scheduling while maintaining flexibility for different use cases and future enhancements.
