# Domain-Agnostic Healthcare Scheduling Framework

A comprehensive healthcare scheduling optimization system built with TimeFold Solver in Python, integrated with Google Maps API for real-world routing. This framework demonstrates how to create a domain-agnostic scheduling system that can adapt to various healthcare scenarios while leveraging TimeFold's advanced constraint solving capabilities.

## 🎯 Overview

This framework is specifically designed for **home-based healthcare agencies** and provides comprehensive scheduling capabilities for:

- **Patient Home Visits**: Wound care, medication administration, assessments
- **Mobile Clinician Scheduling**: Nurses, therapists, and aides visiting patients at home
- **Emergency Home Response**: Real-time optimization for urgent patient situations
- **Multi-Region Operations**: Coordinated scheduling across different service areas
- **Specialized Home Care**: Physical therapy, occupational therapy, chronic care management

## 🚀 Key Features

### Core Scheduling Capabilities
- **Domain-Agnostic Design**: Adaptable to various healthcare scheduling scenarios
- **Hierarchical Skill Matching**: Intelligent skill substitution and fallback
- **Real-Time Planning**: Handle emergencies, staff changes, and schedule disruptions
- **Advanced Constraints**: Safety pairing, regulatory compliance, continuity of care
- **Multi-Location Support**: Time zone aware scheduling across facilities

### Integration & Optimization
- **Google Maps Integration**: Real-world travel times and route optimization
- **TimeFold Solver**: Advanced constraint solving with configurable weights
- **Recommendations Engine**: Intelligent suggestions for appointment scheduling
- **Real-Time Updates**: Dynamic schedule adjustments based on traffic and changes

### Healthcare-Specific Features
- **Safety Protocols**: Required pairing for high-risk areas and situations
- **Regulatory Compliance**: Automatic adherence to healthcare labor laws
- **Infection Control**: Staff filtering based on risk factors and protocols
- **Continuity of Care**: Prefer same clinician for related visits
- **Emergency Response**: Priority-based scheduling for urgent situations

## 📋 Quick Start

### Prerequisites

```bash
pip install timefold==1.22.1b0
pip install googlemaps
pip install requests
```

### Environment Setup

```bash
export GOOGLE_API_KEY=AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4
```

### Basic Usage

```python
from healthcare_scheduler import HealthcareSchedulingEngine, SchedulingRequest
from healthcare_domain import HealthcareResource, HealthcareTask, Location

# Initialize the scheduling engine
scheduler = HealthcareSchedulingEngine(google_api_key="your_api_key")

# Create scheduling request
request = SchedulingRequest(
    resources=your_clinicians,
    tasks=your_patient_visits,
    locations=your_locations,
    schedule_date=datetime.date.today(),
    optimization_time_seconds=30
)

# Generate optimized schedule
result = scheduler.create_schedule(request)
print(f"Score: {result.score}")
print(f"Assignment Rate: {result.statistics['assignment_rate']:.1f}%")
```

### Run the Example

```bash
python healthcare_main.py
```

Expected output:
```
🏥 Domain-Agnostic Healthcare Scheduling Framework
============================================================
📍 Locations: 4
👥 Resources: 3
📋 Tasks: 5

🔄 Optimizing schedule...
✅ Optimization completed in 2.34 seconds
📊 Score: 0hard/-150soft

📅 OPTIMIZED SCHEDULE
----------------------------------------

👤 Alice Johnson, RN
  09:00-10:30 | Advanced Wound Care - Johnson | Patient Johnson Home
  11:00-12:15 | Physical Therapy - Rehabilitation | Patient Johnson Home

👤 Bob Smith, LPN
  13:00-14:00 | Basic Wound Care - Smith | Patient Smith Home

📈 STATISTICS
--------------------
Assignment Rate: 80.0%
Average Resource Utilization: 65.5%

🚗 TRAVEL SUMMARY
--------------------
Total Distance: 15.2 km
Total Travel Time: 1.2 hours
Estimated Fuel Cost: $12.50
```

## 🏗️ Architecture

The framework follows a layered architecture based on TimeFold Solver patterns:

```
┌─────────────────────────────────────────────────────────┐
│                Healthcare Scheduler                     │
│  (Main orchestration and optimization engine)          │
├─────────────────────────────────────────────────────────┤
│              Constraint System                          │
│  (Healthcare-specific rules and optimization goals)    │
├─────────────────────────────────────────────────────────┤
│               Domain Model                              │
│  (Resources, Tasks, Locations, Skills, Time Windows)   │
├─────────────────────────────────────────────────────────┤
│            External Integrations                        │
│  (Google Maps API, TimeFold Solver, Real-time data)   │
└─────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. Healthcare Domain Model (`healthcare_domain.py`)
- **Abstract Base Classes**: Resource, Task for domain-agnostic design
- **Healthcare Entities**: HealthcareResource, HealthcareTask with medical-specific attributes
- **Supporting Classes**: Location, Skill, TimeWindow, Priority enums
- **Planning Solution**: HealthcareSchedule with TimeFold annotations

#### 2. Constraint System (`healthcare_constraints.py`)
- **Hard Constraints**: Skill requirements, capacity limits, time windows
- **Soft Constraints**: Travel optimization, workload balance, continuity of care
- **Configurable Weights**: Adapt constraint importance for different scenarios
- **Healthcare-Specific Rules**: Safety pairing, regulatory compliance

#### 3. Google Maps Integration (`google_maps_service.py`)
- **Real-Time Travel Data**: Actual driving times and distances
- **Route Optimization**: Multi-stop route planning for healthcare visits
- **Traffic Awareness**: Dynamic updates based on current traffic conditions
- **Healthcare Routing**: Safety-aware routing with accessibility considerations

#### 4. Scheduling Engine (`healthcare_scheduler.py`)
- **Main Orchestration**: Coordinates all components for optimization
- **Real-Time Updates**: Handle schedule changes and emergencies
- **Recommendations**: Suggest optimal assignments for new tasks
- **Statistics & Analytics**: Comprehensive reporting and insights

## 🎯 Use Cases

### 1. Home Healthcare Scheduling
```python
# Configure for home healthcare
constraint_config = ConstraintConfiguration(
    minimize_travel=ConstraintWeight("0hard", "5soft"),
    prefer_continuity=ConstraintWeight("0hard", "15soft"),
    safety_pairing_required=True
)

# Example: Wound care nurse scheduling
wound_care_task = HealthcareTask(
    id="wound_care_001",
    name="Advanced Wound Care",
    required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
    duration_minutes=90,
    priority=Priority.HIGH,
    location=patient_home,
    continuity_requirements=["same_clinician_preferred"]
)
```

### 2. Emergency Response Coordination
```python
# Emergency task with high priority
emergency_task = HealthcareTask(
    id="emergency_001",
    name="Critical Assessment",
    required_skills=[HealthcareSkills.EMERGENCY_RESPONSE],
    duration_minutes=120,
    priority=Priority.CRITICAL,
    time_windows=[emergency_window],
    equipment_required=["emergency_kit", "defibrillator"]
)

# Real-time optimization for emergency
result = scheduler.update_schedule_real_time(
    current_schedule=active_schedule,
    changes=[{"type": "ADD_EMERGENCY_TASK", "task": emergency_task}]
)
```

### 3. Multi-Facility Operations
```python
# Locations across different facilities
clinic_a = Location(
    id="clinic_a",
    name="Downtown Clinic",
    location_type=LocationType.CLINIC,
    timezone="America/New_York"
)

clinic_b = Location(
    id="clinic_b", 
    name="Suburban Clinic",
    location_type=LocationType.CLINIC,
    timezone="America/New_York"
)

# Resource that can work at multiple facilities
mobile_nurse = HealthcareResource(
    id="mobile_nurse_001",
    name="Mobile Nurse Sarah",
    skills=[HealthcareSkills.MEDICATION_IV, HealthcareSkills.WOUND_CARE_BASIC],
    availability_windows=[full_day_window],
    location=None  # Can travel between facilities
)
```

## 🔧 Configuration

The framework is highly configurable to adapt to different healthcare scenarios:

```python
# Domain-specific configurations
healthcare_configs = {
    "home_healthcare": {
        "max_travel_time_minutes": 45,
        "prefer_continuity_weight": 15,
        "safety_pairing_required": True,
        "infection_control_strict": True
    },
    
    "hospital_scheduling": {
        "max_consecutive_hours": 12,
        "mandatory_break_minutes": 30,
        "skill_substitution_penalty": 5,
        "equipment_coordination": True
    },
    
    "emergency_response": {
        "response_time_limit_minutes": 30,
        "priority_weight_multiplier": 100,
        "real_time_optimization": True,
        "safety_pairing_mandatory": True
    },
    
    "specialized_care": {
        "interdisciplinary_coordination": True,
        "equipment_scheduling": True,
        "progress_tracking": True,
        "family_involvement": True
    }
}
```

## 📊 TimeFold Integration Analysis

### Java vs Python Implementation Patterns

Based on the TimeFold repository analysis, our Python implementation follows these key patterns:

#### 1. **Domain Modeling** (Java → Python)
```java
// Java TimeFold Pattern
@PlanningEntity
public class Visit {
    @PlanningVariable(valueRangeProviderRefs = {"clinicianRange"})
    private Clinician clinician;
    
    @PlanningVariable(valueRangeProviderRefs = {"timeRange"})
    private LocalDateTime startTime;
}
```

```python
# Python Implementation
@planning_entity
@dataclass
class HealthcareTask:
    assigned_resource: Annotated[
        Optional[HealthcareResource], 
        PlanningVariable(value_range_provider_refs=['resourceRange'])
    ] = field(default=None)
    
    start_time: Annotated[
        Optional[datetime.datetime], 
        PlanningVariable(value_range_provider_refs=['timeRange'])
    ] = field(default=None)
```

#### 2. **Constraint Definition** (Java → Python)
```java
// Java Constraint Pattern
@ConstraintProvider
public class HealthcareConstraints {
    public Constraint skillMismatch(ConstraintFactory cf) {
        return cf.forEach(Visit.class)
                .filter(visit -> !visit.getClinician().hasSkill(visit.getRequiredSkill()))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Skill mismatch");
    }
}
```

```python
# Python Implementation
@constraint_provider
def define_healthcare_constraints(constraint_factory):
    return [skill_requirement_violation(constraint_factory)]

def skill_requirement_violation(cf):
    return (cf.for_each(HealthcareTask)
            .filter(lambda task: task.assigned_resource is not None and
                    not task.assigned_resource.can_perform_skill(task.required_skills[0]))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill requirement violation'))
```

#### 3. **Solver Configuration** (Java → Python)
```java
// Java Configuration
SolverConfig solverConfig = new SolverConfig()
    .withSolutionClass(Schedule.class)
    .withEntityClasses(Visit.class)
    .withConstraintProviderClass(HealthcareConstraints.class)
    .withTerminationConfig(new TerminationConfig()
        .withSpentLimit(Duration.ofSeconds(30)));
```

```python
# Python Implementation
solver_config = SolverConfig(
    solution_class=HealthcareSchedule,
    entity_class_list=[HealthcareTask],
    score_director_factory_config=ScoreDirectorFactoryConfig(
        constraint_provider_function=define_healthcare_constraints
    ),
    termination_config=TerminationConfig(
        spent_limit=Duration(seconds=30)
    )
)
```

### Key Insights from TimeFold Analysis

1. **Constraint Streams**: TimeFold's constraint stream API is powerful for expressing complex business rules
2. **Value Range Providers**: Essential for defining the search space for planning variables
3. **Score Calculation**: Hard/soft score separation allows for feasibility vs optimization trade-offs
4. **Incremental Scoring**: TimeFold automatically handles incremental score updates for performance
5. **Real-Time Planning**: Support for continuous planning with schedule updates

## 📚 Documentation Structure

- [Architecture Overview](architecture.md) - System design and components
- [Domain Model](domain-model.md) - Core abstractions and healthcare entities  
- [Constraint System](constraints.md) - Scheduling rules and optimization goals
- [Google Maps Integration](google-maps.md) - Real-world routing and travel optimization
- [Real-Time Planning](real-time-planning.md) - Dynamic schedule adjustments
- [Configuration Guide](configuration.md) - Customizing for different healthcare scenarios
- [API Reference](api-reference.md) - Complete API documentation
- [Examples](examples/) - Use case implementations and tutorials

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](contributing.md) for details on:

- Code style and standards
- Testing requirements  
- Documentation guidelines
- Pull request process

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## 🙏 Acknowledgments

- **TimeFold AI** for the powerful constraint solving engine
- **Google Maps Platform** for real-world routing capabilities
- **Healthcare professionals** who provided domain expertise and feedback

---

*This framework demonstrates the power of domain-agnostic design combined with healthcare-specific optimizations, providing a solid foundation for various healthcare scheduling applications.*
