# Google Maps Integration for Healthcare Scheduling

## Overview

The Google Maps integration provides real-world routing capabilities for healthcare scheduling, enabling accurate travel time calculations, route optimization, and traffic-aware scheduling decisions. This integration is essential for home healthcare, mobile clinics, and any healthcare service that involves travel between locations.

## Key Features

### 1. Real-Time Travel Data
- **Accurate Travel Times**: Current driving times between healthcare locations
- **Traffic Awareness**: Real-time traffic conditions affecting travel duration
- **Multiple Transportation Modes**: Driving, walking, public transit support
- **Distance Calculations**: Precise distance measurements for cost estimation

### 2. Route Optimization
- **Multi-Stop Routes**: Optimize sequences of patient visits
- **Healthcare-Specific Routing**: Consider safety ratings and accessibility
- **Time Window Constraints**: Route planning with appointment time windows
- **Emergency Routing**: Priority routing for urgent healthcare needs

### 3. Constraint Integration
- **TimeFold Integration**: Travel data feeds into constraint calculations
- **Dynamic Constraints**: Real-time updates based on traffic conditions
- **Cost Optimization**: Minimize travel costs and time
- **Feasibility Checking**: Ensure routes are physically possible

## Implementation Architecture

```python
class GoogleMapsHealthcareService:
    """
    Main service class for Google Maps integration
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.gmaps = googlemaps.Client(key=api_key)
        self.travel_cache = {}  # Performance optimization
    
    # Core Methods
    def get_travel_info(self, origin: Location, destination: Location) -> TravelInfo
    def get_travel_matrix(self, origins: List[Location], destinations: List[Location]) -> Dict
    def optimize_healthcare_route(self, visits: List[Tuple[Location, datetime]]) -> RouteOptimization
    def get_real_time_traffic_updates(self, active_routes: List[Dict]) -> Dict
```

## Core Components

### 1. Travel Information Calculation

```python
@dataclass
class TravelInfo:
    """Comprehensive travel data between two locations"""
    distance_meters: int
    duration_seconds: int
    duration_in_traffic_seconds: Optional[int]
    start_address: str
    end_address: str
    route_polyline: str = ""

def get_travel_info(
    self,
    origin: Location,
    destination: Location,
    departure_time: datetime.datetime = None,
    mode: str = "driving"
) -> TravelInfo:
    """
    Get detailed travel information between healthcare locations
    
    Features:
    - Real-time traffic data
    - Multiple transportation modes
    - Departure time optimization
    - Fallback to estimation if API unavailable
    """
```

**Usage Example**:
```python
# Calculate travel time between patient homes
travel_info = maps_service.get_travel_info(
    origin=patient_home_1,
    destination=patient_home_2,
    departure_time=datetime.datetime(2025, 1, 15, 10, 0),
    mode="driving"
)

print(f"Distance: {travel_info.distance_meters / 1000:.1f} km")
print(f"Travel time: {travel_info.duration_seconds / 60:.0f} minutes")
print(f"With traffic: {travel_info.duration_in_traffic_seconds / 60:.0f} minutes")
```

### 2. Travel Matrix for Bulk Calculations

```python
def get_travel_matrix(
    self,
    origins: List[Location],
    destinations: List[Location],
    departure_time: datetime.datetime = None
) -> Dict[Tuple[str, str], TravelInfo]:
    """
    Efficient bulk calculation of travel times between multiple locations
    
    Optimizations:
    - Single API call for multiple origin-destination pairs
    - Caching for repeated requests
    - Batch processing for large datasets
    """
```

**Usage Example**:
```python
# Calculate travel times between all patient locations
patient_locations = [home_1, home_2, home_3, clinic]
travel_matrix = maps_service.get_travel_matrix(
    origins=patient_locations,
    destinations=patient_locations,
    departure_time=datetime.datetime.now()
)

# Access specific travel time
travel_time = travel_matrix[("home_1", "home_2")].duration_seconds
```

### 3. Healthcare Route Optimization

```python
@dataclass
class RouteOptimization:
    """Optimized route with healthcare-specific considerations"""
    locations: List[Location]
    total_distance_meters: int
    total_duration_seconds: int
    estimated_fuel_cost: float
    safety_score: float
    waypoint_order: List[int]

def optimize_healthcare_route(
    self,
    start_location: Location,
    visit_locations: List[Tuple[Location, datetime.datetime]],
    end_location: Optional[Location] = None
) -> RouteOptimization:
    """
    Optimize route for healthcare visits considering:
    - Time windows for appointments
    - Safety ratings of locations
    - Accessibility requirements
    - Emergency priority handling
    """
```

**Healthcare-Specific Route Scoring**:
```python
def _calculate_healthcare_route_score(
    self,
    travel_info: TravelInfo,
    destination: Location,
    current_time: datetime.datetime
) -> float:
    """
    Calculate route score with healthcare priorities:
    - Base score from travel time
    - Safety penalty for unsafe areas during certain hours
    - Accessibility bonus for wheelchair-accessible locations
    - Parking availability consideration
    """
    
    base_score = travel_info.duration_seconds
    
    # Safety considerations
    if destination.safety_rating < 3 and (current_time.hour < 8 or current_time.hour > 18):
        base_score *= 1.5  # Penalty for unsafe areas at night
    
    # Accessibility preferences
    if "wheelchair_accessible" in destination.accessibility_features:
        base_score *= 0.9  # Slight preference for accessible locations
    
    # Parking availability
    if not destination.parking_available:
        base_score *= 1.2  # Penalty for locations without parking
    
    return base_score
```

### 4. Real-Time Traffic Updates

```python
def get_real_time_traffic_updates(
    self,
    active_routes: List[Dict[str, any]]
) -> Dict[str, Dict[str, any]]:
    """
    Monitor active healthcare routes for traffic delays
    
    Features:
    - Real-time traffic monitoring
    - Automatic delay detection
    - Schedule adjustment recommendations
    - Patient notification triggers
    """
```

**Real-Time Update Flow**:
```python
# Monitor active clinician routes
active_routes = [
    {
        "resource_id": "nurse_alice",
        "current_location": {"lat": 40.7128, "lng": -74.0060},
        "upcoming_tasks": [
            {
                "id": "visit_001",
                "location": patient_home_1,
                "expected_travel_seconds": 1200,
                "scheduled_time": "10:00"
            }
        ]
    }
]

# Get real-time updates
updates = maps_service.get_real_time_traffic_updates(active_routes)

# Process delays
for resource_id, update in updates.items():
    if update["requires_reschedule"]:
        # Trigger schedule adjustment
        scheduler.update_schedule_real_time(
            current_schedule,
            [{"type": "TRAFFIC_DELAY", "resource_id": resource_id, "delay": update}]
        )
```

## TimeFold Integration

### 1. Constraint Generation

```python
def calculate_healthcare_constraints(
    self,
    tasks: List[HealthcareTask],
    resources: List[HealthcareResource]
) -> Dict[str, any]:
    """
    Generate travel-based constraints for TimeFold optimization
    
    Output:
    - Maximum travel time limits
    - Travel time penalties between locations
    - Location accessibility constraints
    - Cost optimization parameters
    """
    
    constraints = {
        "max_travel_time_minutes": 45,
        "max_daily_distance_km": 200,
        "travel_time_penalties": {},
        "location_accessibility": {}
    }
    
    # Calculate travel penalties
    task_locations = [task.location for task in tasks if task.location]
    travel_matrix = self.get_travel_matrix(task_locations, task_locations)
    
    for (origin_id, dest_id), travel_info in travel_matrix.items():
        travel_minutes = travel_info.duration_seconds / 60
        
        if travel_minutes > constraints["max_travel_time_minutes"]:
            penalty = int((travel_minutes - constraints["max_travel_time_minutes"]) / 5)
            constraints["travel_time_penalties"][(origin_id, dest_id)] = penalty
    
    return constraints
```

### 2. Dynamic Constraint Updates

```python
# Integration with TimeFold constraints
def travel_time_constraint(cf):
    """
    TimeFold constraint that uses real Google Maps travel data
    """
    def excessive_travel_time(task1, task2):
        if (task1.assigned_resource != task2.assigned_resource or
            task1.location is None or task2.location is None):
            return False
        
        # Get real travel time from Google Maps
        travel_info = maps_service.get_travel_info(task1.location, task2.location)
        travel_minutes = travel_info.duration_seconds / 60
        
        return travel_minutes > 45  # Configurable limit
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(excessive_travel_time)
            .penalize(HardSoftScore.ONE_SOFT, 
                     lambda t1, t2: calculate_travel_penalty(t1, t2))
            .as_constraint('Excessive travel time'))
```

## Performance Optimization

### 1. Caching Strategy

```python
class GoogleMapsHealthcareService:
    def __init__(self, api_key: str):
        self.travel_cache = {}  # In-memory cache
        self.cache_ttl = 3600   # 1 hour cache lifetime
    
    def get_travel_info(self, origin: Location, destination: Location) -> TravelInfo:
        # Check cache first
        cache_key = f"{origin.id}-{destination.id}-{datetime.now().hour}"
        if cache_key in self.travel_cache:
            cached_result, timestamp = self.travel_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return cached_result
        
        # Make API call and cache result
        travel_info = self._fetch_from_api(origin, destination)
        self.travel_cache[cache_key] = (travel_info, time.time())
        return travel_info
```

### 2. Batch Processing

```python
def optimize_multiple_routes(
    self,
    route_requests: List[Dict[str, any]]
) -> List[RouteOptimization]:
    """
    Optimize multiple routes in batch for better API efficiency
    """
    
    # Collect all unique locations
    all_locations = set()
    for request in route_requests:
        all_locations.update(request["locations"])
    
    # Single travel matrix call for all locations
    travel_matrix = self.get_travel_matrix(
        list(all_locations), 
        list(all_locations)
    )
    
    # Optimize each route using cached travel data
    optimized_routes = []
    for request in route_requests:
        route = self._optimize_single_route(request, travel_matrix)
        optimized_routes.append(route)
    
    return optimized_routes
```

### 3. Fallback Mechanisms

```python
def _estimate_travel_fallback(self, origin: Location, destination: Location) -> TravelInfo:
    """
    Fallback travel estimation when Google Maps API is unavailable
    Uses haversine distance formula with average speed assumptions
    """
    import math
    
    # Haversine distance calculation
    lat1, lon1 = math.radians(origin.latitude), math.radians(origin.longitude)
    lat2, lon2 = math.radians(destination.latitude), math.radians(destination.longitude)
    
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    distance_meters = int(6371000 * c)  # Earth's radius in meters
    
    # Estimate duration (40 km/h average speed in urban areas)
    duration_seconds = int(distance_meters / 40000 * 3600)
    
    return TravelInfo(
        distance_meters=distance_meters,
        duration_seconds=duration_seconds,
        duration_in_traffic_seconds=None,
        start_address=origin.address,
        end_address=destination.address
    )
```

## Configuration and Setup

### 1. API Key Management

```python
# Environment variable setup
export GOOGLE_API_KEY=AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4

# Secure configuration in production
import os
from typing import Optional

class GoogleMapsConfig:
    def __init__(self):
        self.api_key = os.getenv('GOOGLE_API_KEY')
        if not self.api_key:
            raise ValueError("Google Maps API key is required")
        
        self.rate_limit = int(os.getenv('GOOGLE_MAPS_RATE_LIMIT', '50'))
        self.cache_ttl = int(os.getenv('GOOGLE_MAPS_CACHE_TTL', '3600'))
        self.fallback_enabled = os.getenv('GOOGLE_MAPS_FALLBACK', 'true').lower() == 'true'
```

### 2. Healthcare-Specific Configuration

```python
healthcare_routing_config = {
    "emergency_response": {
        "max_response_time_minutes": 30,
        "priority_routing": True,
        "avoid_traffic": True,
        "use_fastest_route": True
    },
    
    "routine_visits": {
        "optimize_for": "time",  # or "distance" or "cost"
        "avoid_tolls": True,
        "prefer_highways": False,
        "consider_parking": True
    },
    
    "safety_considerations": {
        "avoid_unsafe_areas": True,
        "night_routing_restrictions": True,
        "minimum_safety_rating": 3,
        "require_parking": True
    }
}
```

## Error Handling and Monitoring

### 1. API Error Handling

```python
def get_travel_info_with_retry(self, origin: Location, destination: Location) -> TravelInfo:
    """
    Robust travel info retrieval with retry logic and error handling
    """
    max_retries = 3
    retry_delay = 1  # seconds
    
    for attempt in range(max_retries):
        try:
            return self._fetch_travel_info(origin, destination)
        
        except googlemaps.exceptions.ApiError as e:
            if e.status == "OVER_QUERY_LIMIT":
                # Rate limit exceeded - wait and retry
                time.sleep(retry_delay * (2 ** attempt))
                continue
            elif e.status == "ZERO_RESULTS":
                # No route found - use fallback
                return self._estimate_travel_fallback(origin, destination)
            else:
                # Other API errors - log and fallback
                logger.error(f"Google Maps API error: {e}")
                return self._estimate_travel_fallback(origin, destination)
        
        except Exception as e:
            logger.error(f"Unexpected error in travel calculation: {e}")
            if attempt == max_retries - 1:
                return self._estimate_travel_fallback(origin, destination)
    
    # Final fallback
    return self._estimate_travel_fallback(origin, destination)
```

### 2. Performance Monitoring

```python
import time
from dataclasses import dataclass
from typing import Dict

@dataclass
class PerformanceMetrics:
    api_calls_count: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    average_response_time: float = 0.0
    error_count: int = 0

class GoogleMapsHealthcareService:
    def __init__(self, api_key: str):
        self.metrics = PerformanceMetrics()
    
    def get_performance_report(self) -> Dict[str, any]:
        """Generate performance report for monitoring"""
        cache_hit_rate = (self.metrics.cache_hits / 
                         (self.metrics.cache_hits + self.metrics.cache_misses) * 100
                         if (self.metrics.cache_hits + self.metrics.cache_misses) > 0 else 0)
        
        return {
            "api_calls": self.metrics.api_calls_count,
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "average_response_time": f"{self.metrics.average_response_time:.2f}s",
            "error_rate": f"{self.metrics.error_count / max(1, self.metrics.api_calls_count) * 100:.1f}%"
        }
```

This Google Maps integration provides a robust foundation for real-world healthcare scheduling, enabling accurate travel calculations and route optimization while maintaining performance and reliability through caching, fallback mechanisms, and comprehensive error handling.
