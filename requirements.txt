# TimeFold Solver for optimization
timefold==1.22.1b0  # Latest confirmed release

# Google Maps API integration
googlemaps>=4.11.0  # Updated to newest version

# HTTP requests for API calls
requests>=2.33.0  # Latest stable update
aiohttp>=3.9.2  # Latest with improved performance

# Data handling and utilities
dataclasses-json>=0.6.4  # Recent improvements in serialization
python-dateutil>=2.8.3  # Latest fixes

# Timezone handling (from TimeFold analysis)
pytz>=2024.1  # Maintained, latest available update
zoneinfo>=0.2.2; python_version >= "3.9"

# Optional: For enhanced data processing
pandas>=2.1.1  # Latest for better efficiency
numpy>=1.26.0  # Updated for stability

# Optional: For configuration management
pydantic>=2.5.0  # Improved validation features
python-dotenv>=1.1.0  # Latest minor fixes

# Optional: For logging and monitoring
structlog>=24.1.0  # Upgraded for better async logging

# Development dependencies (optional)
pytest>=8.0.0  # Latest version for testing improvements
pytest-asyncio>=0.22.1  # Updated async testing support
black>=24.2.0  # Latest with formatting enhancements
mypy>=1.8.0  # Improved type-checking
