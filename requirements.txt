# TimeFold Solver for optimization
timefold==1.22.1b0

# Google Maps API integration
googlemaps>=4.10.0

# HTTP requests for API calls
requests>=2.31.0
aiohttp>=3.8.0

# Data handling and utilities
dataclasses-json>=0.6.0
python-dateutil>=2.8.0

# Timezone handling (from TimeFold analysis)
pytz>=2023.3
zoneinfo>=0.2.1; python_version >= "3.9"

# Optional: For enhanced data processing
pandas>=2.0.0
numpy>=1.24.0

# Optional: For configuration management
pydantic>=2.0.0
python-dotenv>=1.0.0

# Optional: For logging and monitoring
structlog>=23.0.0

# Development dependencies (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
mypy>=1.5.0
