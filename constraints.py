import datetime
from timefold.solver.score import constraint_provider, ConstraintCollectors, Joiners
from timefold.solver.score import HardSoftScore
from domain import Visit

@constraint_provider
def define_constraints(constraint_factory):
    return [
        skill_mismatch(constraint_factory),
        service_area_mismatch(constraint_factory),
        patient_window_violation(constraint_factory),
        clinician_unavailable(constraint_factory),
        clinician_overlap(constraint_factory),
        max_visits_per_clinician(constraint_factory),
        dependency_order(constraint_factory),
        dependency_same_clinician(constraint_factory)
    ]

# ---- constraints ----
def skill_mismatch(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None and
                    v.required_skill not in v.clinician.skills)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill mismatch'))

def service_area_mismatch(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None and
                    v.service_area not in v.clinician.service_areas)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Service area mismatch'))

def patient_window_violation(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.start is None or
                    v.start < v.window_start or
                    v.start + datetime.timedelta(minutes=v.duration_minutes) > v.window_end)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Outside patient window'))

def clinician_unavailable(cf):
    def unavailable(v):
        if v.clinician is None or v.start is None:
            return False
        end = v.start + datetime.timedelta(minutes=v.duration_minutes)
        return not any(slot.contains_range(v.start, end)
                       for slot in v.clinician.availability)

    return (cf.for_each(Visit)
            .filter(unavailable)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Clinician unavailable'))

def clinician_overlap(cf):
    def overlap(v1, v2):
        if v1.clinician != v2.clinician or v1.start is None or v2.start is None:
            return False
        return not (v1.start + datetime.timedelta(minutes=v1.duration_minutes) <= v2.start or
                    v2.start + datetime.timedelta(minutes=v2.duration_minutes) <= v1.start)

    return (cf.for_each_unique_pair(Visit)
            .filter(overlap)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Visits overlap'))

def max_visits_per_clinician(cf):
    return (cf.for_each(Visit)
            .group_by(lambda v: v.clinician,
                      ConstraintCollectors.count())
            .filter(lambda c, count: c is not None and count > c.max_daily_visits)
            .penalize(HardSoftScore.ONE_HARD,
                      lambda c, count: count - c.max_daily_visits)
            .as_constraint('Clinician overbooked'))

# dependency order
def dependency_order(cf):
    def is_dependency(pre, post):
        return any(d.predecessor_id == pre.id for d in post.dependencies)

    def order_violated(pre, post):
        return pre.start is not None and post.start is not None and post.start < pre.start + datetime.timedelta(minutes=pre.duration_minutes)

    return (cf.for_each(Visit)
            .join(Visit,
                  Joiners.filtering(is_dependency))
            .filter(order_violated)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order'))

# enforce same clinician if flagged
def dependency_same_clinician(cf):
    def needs_same(pre, post):
        return any(d.predecessor_id == pre.id and d.same_clinician_required for d in post.dependencies)

    return (cf.for_each(Visit)
            .join(Visit,
                  Joiners.filtering(needs_same))
            .filter(lambda pre, post: pre.clinician is not None and post.clinician is not None and
                    pre.clinician != post.clinician)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Same clinician dependency'))
