"""
Google Maps Integration Service for Healthcare Scheduling
Provides real-world travel times and route optimization
"""

import os
import googlemaps
import datetime
from typing import List, Dict, Tu<PERSON>, Optional
from dataclasses import dataclass
import asyncio
import aiohttp
import json

from healthcare_domain import Location, HealthcareTask, HealthcareResource


@dataclass
class TravelInfo:
    """Travel information between two locations"""
    distance_meters: int
    duration_seconds: int
    duration_in_traffic_seconds: Optional[int]
    start_address: str
    end_address: str
    route_polyline: str = ""


@dataclass
class RouteOptimization:
    """Optimized route information"""
    locations: List[Location]
    total_distance_meters: int
    total_duration_seconds: int
    estimated_fuel_cost: float
    safety_score: float
    waypoint_order: List[int]


class GoogleMapsHealthcareService:
    """
    Google Maps service specifically designed for healthcare scheduling
    Integrates with TimeFold constraints for real-world routing
    """
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('GOOGLE_API_KEY')
        if not self.api_key:
            raise ValueError("Google Maps API key is required")
        
        self.gmaps = googlemaps.Client(key=self.api_key)
        self.travel_cache = {}  # Cache for repeated requests
        
    def get_travel_info(
        self,
        origin: Location,
        destination: Location,
        departure_time: datetime.datetime = None,
        mode: str = "driving"
    ) -> TravelInfo:
        """
        Get travel information between two healthcare locations
        """
        if departure_time is None:
            departure_time = datetime.datetime.now()
        
        # Check cache first
        cache_key = f"{origin.id}-{destination.id}-{departure_time.hour}"
        if cache_key in self.travel_cache:
            return self.travel_cache[cache_key]
        
        try:
            origin_coords = (origin.latitude, origin.longitude)
            dest_coords = (destination.latitude, destination.longitude)
            
            result = self.gmaps.directions(
                origin=origin_coords,
                destination=dest_coords,
                mode=mode,
                departure_time=departure_time,
                traffic_model="best_guess",
                units="metric"
            )
            
            if not result:
                # Fallback to straight-line estimation
                return self._estimate_travel_fallback(origin, destination)
            
            route = result[0]
            leg = route['legs'][0]
            
            travel_info = TravelInfo(
                distance_meters=leg['distance']['value'],
                duration_seconds=leg['duration']['value'],
                duration_in_traffic_seconds=leg.get('duration_in_traffic', {}).get('value'),
                start_address=leg['start_address'],
                end_address=leg['end_address'],
                route_polyline=route['overview_polyline']['points']
            )
            
            # Cache the result
            self.travel_cache[cache_key] = travel_info
            return travel_info
            
        except Exception as e:
            print(f"Error getting travel info: {e}")
            return self._estimate_travel_fallback(origin, destination)
    
    def get_travel_matrix(
        self,
        origins: List[Location],
        destinations: List[Location],
        departure_time: datetime.datetime = None
    ) -> Dict[Tuple[str, str], TravelInfo]:
        """
        Get travel matrix for multiple origins and destinations
        Optimized for healthcare route planning
        """
        if departure_time is None:
            departure_time = datetime.datetime.now()
        
        origin_coords = [(loc.latitude, loc.longitude) for loc in origins]
        dest_coords = [(loc.latitude, loc.longitude) for loc in destinations]
        
        try:
            result = self.gmaps.distance_matrix(
                origins=origin_coords,
                destinations=dest_coords,
                mode="driving",
                departure_time=departure_time,
                traffic_model="best_guess",
                units="metric"
            )
            
            travel_matrix = {}
            for i, row in enumerate(result['rows']):
                for j, element in enumerate(row['elements']):
                    if element['status'] == 'OK':
                        travel_matrix[(origins[i].id, destinations[j].id)] = TravelInfo(
                            distance_meters=element['distance']['value'],
                            duration_seconds=element['duration']['value'],
                            duration_in_traffic_seconds=element.get('duration_in_traffic', {}).get('value'),
                            start_address=result['origin_addresses'][i],
                            end_address=result['destination_addresses'][j]
                        )
            
            return travel_matrix
            
        except Exception as e:
            print(f"Error getting travel matrix: {e}")
            return {}
    
    def optimize_healthcare_route(
        self,
        start_location: Location,
        visit_locations: List[Tuple[Location, datetime.datetime]],
        end_location: Optional[Location] = None
    ) -> RouteOptimization:
        """
        Optimize route for healthcare visits considering time windows and priorities
        """
        all_locations = [start_location] + [loc for loc, _ in visit_locations]
        if end_location:
            all_locations.append(end_location)
        
        # Get travel matrix
        travel_matrix = self.get_travel_matrix(all_locations, all_locations)
        
        # Simple greedy optimization (in production, use more sophisticated algorithms)
        optimized_route = self._greedy_route_optimization(
            all_locations, visit_locations, travel_matrix
        )
        
        return optimized_route
    
    def calculate_healthcare_constraints(
        self,
        tasks: List[HealthcareTask],
        resources: List[HealthcareResource]
    ) -> Dict[str, any]:
        """
        Calculate travel-based constraints for TimeFold integration
        """
        constraints = {
            "max_travel_time_minutes": 45,
            "max_daily_distance_km": 200,
            "travel_time_penalties": {},
            "location_accessibility": {}
        }
        
        # Calculate travel times between all task locations
        task_locations = [task.location for task in tasks if task.location]
        unique_locations = list(set(task_locations))
        
        if len(unique_locations) > 1:
            travel_matrix = self.get_travel_matrix(unique_locations, unique_locations)
            
            for (origin_id, dest_id), travel_info in travel_matrix.items():
                travel_minutes = travel_info.duration_seconds / 60
                
                # Create penalty structure for TimeFold constraints
                if travel_minutes > constraints["max_travel_time_minutes"]:
                    penalty = int((travel_minutes - constraints["max_travel_time_minutes"]) / 5)
                    constraints["travel_time_penalties"][(origin_id, dest_id)] = penalty
        
        return constraints
    
    def get_real_time_traffic_updates(
        self,
        active_routes: List[Dict[str, any]]
    ) -> Dict[str, Dict[str, any]]:
        """
        Get real-time traffic updates for active healthcare routes
        """
        updates = {}
        
        for route in active_routes:
            resource_id = route["resource_id"]
            current_location = route["current_location"]
            next_tasks = route["upcoming_tasks"]
            
            if next_tasks:
                next_task = next_tasks[0]
                
                travel_info = self.get_travel_info(
                    origin=Location(
                        id="current",
                        name="Current Location",
                        latitude=current_location["lat"],
                        longitude=current_location["lng"],
                        location_type="current",
                        address=""
                    ),
                    destination=next_task["location"],
                    departure_time=datetime.datetime.now()
                )
                
                expected_duration = next_task.get("expected_travel_seconds", 0)
                actual_duration = travel_info.duration_in_traffic_seconds or travel_info.duration_seconds
                
                if abs(actual_duration - expected_duration) > 300:  # 5 minutes difference
                    updates[resource_id] = {
                        "next_task_id": next_task["id"],
                        "original_travel_time": expected_duration,
                        "updated_travel_time": actual_duration,
                        "delay_minutes": (actual_duration - expected_duration) / 60,
                        "requires_reschedule": abs(actual_duration - expected_duration) > 900
                    }
        
        return updates
    
    def _estimate_travel_fallback(self, origin: Location, destination: Location) -> TravelInfo:
        """
        Fallback travel estimation using straight-line distance
        """
        # Simple haversine distance calculation
        import math
        
        lat1, lon1 = math.radians(origin.latitude), math.radians(origin.longitude)
        lat2, lon2 = math.radians(destination.latitude), math.radians(destination.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth's radius in meters
        distance_meters = int(6371000 * c)
        
        # Estimate duration (assuming 40 km/h average speed in urban areas)
        duration_seconds = int(distance_meters / 40000 * 3600)
        
        return TravelInfo(
            distance_meters=distance_meters,
            duration_seconds=duration_seconds,
            duration_in_traffic_seconds=None,
            start_address=origin.address,
            end_address=destination.address
        )
    
    def _greedy_route_optimization(
        self,
        locations: List[Location],
        visit_schedule: List[Tuple[Location, datetime.datetime]],
        travel_matrix: Dict[Tuple[str, str], TravelInfo]
    ) -> RouteOptimization:
        """
        Simple greedy route optimization for healthcare visits
        """
        if not locations:
            return RouteOptimization([], 0, 0, 0.0, 5.0, [])
        
        # Start with first location
        route = [locations[0]]
        total_distance = 0
        total_duration = 0
        current_location_idx = 0
        unvisited = list(range(1, len(locations)))
        
        while unvisited:
            best_next = None
            best_travel_info = None
            best_score = float('inf')
            
            for next_idx in unvisited:
                travel_key = (locations[current_location_idx].id, locations[next_idx].id)
                if travel_key in travel_matrix:
                    travel_info = travel_matrix[travel_key]
                    
                    # Score based on distance and healthcare priorities
                    score = travel_info.duration_seconds
                    
                    # Prefer locations with better safety ratings
                    safety_bonus = (locations[next_idx].safety_rating - 3) * 60  # seconds
                    score -= safety_bonus
                    
                    if score < best_score:
                        best_score = score
                        best_next = next_idx
                        best_travel_info = travel_info
            
            if best_next is not None:
                route.append(locations[best_next])
                total_distance += best_travel_info.distance_meters
                total_duration += best_travel_info.duration_seconds
                current_location_idx = best_next
                unvisited.remove(best_next)
            else:
                break
        
        # Calculate estimated fuel cost (rough estimate)
        fuel_cost = (total_distance / 1000) * 0.15  # $0.15 per km
        
        # Calculate safety score
        safety_score = sum(loc.safety_rating for loc in route) / len(route)
        
        return RouteOptimization(
            locations=route,
            total_distance_meters=total_distance,
            total_duration_seconds=total_duration,
            estimated_fuel_cost=fuel_cost,
            safety_score=safety_score,
            waypoint_order=list(range(len(route)))
        )
