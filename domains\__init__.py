"""
Domain-Specific Implementations - 20% Customization Layer
=========================================================

This package contains domain-specific implementations that extend the core
scheduling framework for particular use cases. Each domain provides:

- Domain-specific resource and task classes
- Specialized constraint providers
- Custom scheduling engines
- Domain-specific utilities and integrations

Available Domains:
- healthcare: Home healthcare scheduling with medical constraints
- education: School and university scheduling (future)
- field_service: Field service and maintenance scheduling (future)
- logistics: Delivery and transportation scheduling (future)

Usage:
    from domains.healthcare import HealthcareSchedulingEngine
    from domains.healthcare import HealthcareResource, HealthcareTask
"""

__version__ = "1.0.0"

# Domain registry for dynamic loading
AVAILABLE_DOMAINS = {
    'healthcare': 'domains.healthcare',
    # Future domains:
    # 'education': 'domains.education',
    # 'field_service': 'domains.field_service',
    # 'logistics': 'domains.logistics',
}

def get_domain_module(domain_name: str):
    """Dynamically import a domain module"""
    if domain_name not in AVAILABLE_DOMAINS:
        raise ValueError(f"Unknown domain: {domain_name}. Available: {list(AVAILABLE_DOMAINS.keys())}")
    
    import importlib
    return importlib.import_module(AVAILABLE_DOMAINS[domain_name])

def list_available_domains():
    """Get list of available domain implementations"""
    return list(AVAILABLE_DOMAINS.keys())
