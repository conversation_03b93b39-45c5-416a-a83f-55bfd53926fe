# Troubleshooting and FAQ

## Common Issues and Solutions

### 1. Import and Setup Issues

#### Problem: `ModuleNotFoundError: No module named 'timefold'`
```bash
# Solution: Install TimeFold
pip install timefold==1.22.1b0

# Or if using conda
conda install -c conda-forge timefold
```

#### Problem: `ImportError: cannot import name 'planning_entity'`
```python
# Wrong import
from timefold import planning_entity  # ❌

# Correct import
from timefold.solver.domain import planning_entity  # ✅
```

### 2. Domain Model Issues

#### Problem: Planning variables not being assigned
```python
# Common mistake - missing value range provider
@planning_entity
@dataclass
class Visit:
    clinician: Optional[Clinician] = None  # ❌ No value range specified

# Correct implementation
@planning_entity
@dataclass
class Visit:
    clinician: Annotated[Optional[Clinician], 
                        PlanningVariable(value_range_provider_refs=['clinicianRange'])] = field(default=None)  # ✅
```

#### Problem: `ValueError: No value range provider found`
```python
# Missing value range provider in solution class
@planning_solution
@dataclass
class Schedule:
    clinicians: List[Clinician]  # ❌ Missing ValueRangeProvider

# Correct implementation
@planning_solution
@dataclass
class Schedule:
    clinicians: Annotated[List[Clinician], 
                         ProblemFactCollectionProperty, 
                         ValueRangeProvider(id='clinicianRange')]  # ✅
```

### 3. Constraint Issues

#### Problem: Constraints not being evaluated
```python
# Missing constraint provider registration
def define_constraints(constraint_factory):
    return [
        skill_mismatch,  # ❌ Function reference without calling
    ]

# Correct implementation
def define_constraints(constraint_factory):
    return [
        skill_mismatch(constraint_factory),  # ✅ Call the function
    ]
```

#### Problem: `TypeError: 'NoneType' object is not callable`
```python
# Constraint function not properly defined
def skill_mismatch():  # ❌ Missing constraint_factory parameter
    return cf.for_each(Visit)  # cf is undefined

# Correct implementation
def skill_mismatch(cf):  # ✅ Accept constraint_factory
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None and
                    v.required_skill not in v.clinician.skills)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill mismatch'))
```

### 4. Solver Configuration Issues

#### Problem: Solver runs but finds no solution
```python
# Check termination config - might be too short
termination_config = TerminationConfig(
    spent_limit=Duration(seconds=1)  # ❌ Too short for complex problems
)

# Better configuration
termination_config = TerminationConfig(
    spent_limit=Duration(seconds=30),  # ✅ More time
    best_score_limit=HardSoftScore.ZERO,  # Stop when perfect solution found
    unimproved_spent_limit=Duration(seconds=5)  # Stop if no improvement
)
```

#### Problem: `IllegalStateException: The entity class list is empty`
```python
# Missing entity class configuration
solver_config = SolverConfig(
    solution_class=Schedule,
    # entity_class_list missing  # ❌
)

# Correct configuration
solver_config = SolverConfig(
    solution_class=Schedule,
    entity_class_list=[Visit],  # ✅
)
```

### 5. Performance Issues

#### Problem: Solver is very slow
```python
# Too many time slots
time_range = [datetime(2025, 6, 1, 8, 0) + timedelta(seconds=i) 
              for i in range(36000)]  # ❌ 36,000 time slots (every second)

# Better granularity
time_range = [datetime(2025, 6, 1, 8, 0) + timedelta(minutes=15*i) 
              for i in range(40)]  # ✅ 40 time slots (every 15 minutes)
```

#### Problem: Memory usage is too high
```python
# Inefficient constraint with expensive operations
def inefficient_constraint(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: expensive_calculation(v))  # ❌ Called for every visit
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Inefficient'))

# Optimized version
def efficient_constraint(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None)  # ✅ Quick filter first
            .filter(lambda v: v.start is not None)
            .filter(lambda v: expensive_calculation(v))  # Expensive check last
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Efficient'))
```

## Frequently Asked Questions

### Q1: How do I handle nullable planning variables?

**A**: Use `Optional` types and check for `None` in constraints:

```python
@planning_entity
@dataclass
class Visit:
    clinician: Annotated[Optional[Clinician], PlanningVariable(...)] = field(default=None)

def constraint_with_null_check(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None)  # Always check for None first
            .filter(lambda v: some_other_condition(v))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Safe constraint'))
```

### Q2: How do I implement soft constraints vs hard constraints?

**A**: Use different score types:

```python
# Hard constraint (must be satisfied)
def hard_constraint(cf):
    return (cf.for_each(Visit)
            .filter(violation_condition)
            .penalize(HardSoftScore.ONE_HARD)  # Hard penalty
            .as_constraint('Hard constraint'))

# Soft constraint (optimization goal)
def soft_constraint(cf):
    return (cf.for_each(Visit)
            .filter(suboptimal_condition)
            .penalize(HardSoftScore.ONE_SOFT)  # Soft penalty
            .as_constraint('Soft constraint'))
```

### Q3: How do I debug why my solution has a bad score?

**A**: Add logging to your constraints:

```python
def debug_constraint(cf):
    def debug_filter(visit):
        violation = visit.clinician is None
        if violation:
            print(f"DEBUG: Unassigned visit: {visit.patient_name}")
        return violation
    
    return (cf.for_each(Visit)
            .filter(debug_filter)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Unassigned visits'))
```

### Q4: How do I handle time zones and different date formats?

**A**: Use timezone-aware datetime objects:

```python
import pytz

# Define timezone
eastern = pytz.timezone('US/Eastern')

# Create timezone-aware datetime
start_time = eastern.localize(datetime.datetime(2025, 6, 1, 8, 0))

# Convert between timezones
utc_time = start_time.astimezone(pytz.UTC)
```

### Q5: How do I implement custom scoring logic?

**A**: Use lambda functions for complex scoring:

```python
def custom_scoring_constraint(cf):
    def calculate_penalty(visit):
        if visit.start is None:
            return 0
        
        # Custom logic: prefer morning appointments
        hour = visit.start.hour
        if hour < 10:
            return 0  # No penalty for morning
        elif hour < 14:
            return 1  # Small penalty for afternoon
        else:
            return 3  # Larger penalty for evening
    
    return (cf.for_each(Visit)
            .penalize(HardSoftScore.ONE_SOFT, calculate_penalty)
            .as_constraint('Morning preference'))
```

### Q6: How do I handle dependencies between visits?

**A**: Use joins to connect related visits:

```python
def dependency_constraint(cf):
    def is_dependent(predecessor, successor):
        return any(dep.predecessor_id == predecessor.id 
                  for dep in successor.dependencies)
    
    def order_violation(pre, post):
        if pre.start is None or post.start is None:
            return False
        pre_end = pre.start + timedelta(minutes=pre.duration_minutes)
        return post.start < pre_end
    
    return (cf.for_each(Visit)
            .join(Visit, Joiners.filtering(is_dependent))
            .filter(order_violation)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order'))
```

### Q7: How do I limit the search space for better performance?

**A**: Use pinning and filtering:

```python
@planning_entity
@dataclass
class Visit:
    # ... other fields ...
    fixed_time: bool = False
    
    def is_pinned(self):
        return self.fixed_time  # TimeFold won't change pinned entities

# Or filter value ranges
def limited_time_range(visit):
    """Only allow morning slots for urgent visits"""
    if visit.is_urgent:
        return [t for t in time_range if t.hour < 12]
    return time_range
```

### Q8: How do I handle real-time updates to the schedule?

**A**: Use problem fact changes:

```python
# Add a new visit to existing solution
def add_visit(solver, new_visit):
    problem_fact_change = ProblemFactChange()
    problem_fact_change.add_entity(new_visit)
    
    # Apply the change
    updated_solution = solver.add_problem_fact_change(problem_fact_change)
    return updated_solution

# Remove a clinician (e.g., called in sick)
def remove_clinician(solver, clinician_to_remove):
    problem_fact_change = ProblemFactChange()
    problem_fact_change.remove_problem_fact(clinician_to_remove)
    
    updated_solution = solver.add_problem_fact_change(problem_fact_change)
    return updated_solution
```

## Best Practices Summary

1. **Always check for `None`** in constraint filters
2. **Use appropriate time granularity** (15-30 minutes, not seconds)
3. **Filter early** in constraint streams for performance
4. **Use meaningful constraint names** for debugging
5. **Start simple** and add complexity gradually
6. **Test constraints individually** before combining
7. **Use proper type hints** for better IDE support
8. **Profile your constraints** to identify bottlenecks

## Getting Help

- **TimeFold Documentation**: https://docs.timefold.ai/
- **GitHub Issues**: https://github.com/TimefoldAI/timefold-solver-python
- **Community Forum**: https://github.com/TimefoldAI/timefold-solver/discussions
- **Stack Overflow**: Tag questions with `timefold`
