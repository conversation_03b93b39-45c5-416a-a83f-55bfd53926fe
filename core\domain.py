"""
Core Domain Model - Abstract Base Classes for Domain-Agnostic Scheduling
========================================================================

This module contains the abstract base classes that define the core scheduling
concepts. These classes are designed to be inherited by domain-specific
implementations (healthcare, education, field service, etc.).

Architecture Diagram:
┌─────────────────────────────────────────────────────────────────────┐
│                        CORE DOMAIN MODEL                           │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │    Resource     │    │      Task       │    │    Location     │  │
│  │   (Abstract)    │    │   (Abstract)    │    │   (Concrete)    │  │
│  │                 │    │                 │    │                 │  │
│  │ + id: str       │    │ + id: str       │    │ + id: str       │  │
│  │ + name: str     │    │ + name: str     │    │ + coordinates   │  │
│  │ + skills: []    │    │ + duration: int │    │ + timezone: str │  │
│  │ + availability │    │ + required_skills│    │ + safety_rating │  │
│  │                 │    │ + time_windows  │    │                 │  │
│  │ + can_perform() │    │ + get_end_time()│    │ + distance_to() │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│           │                       │                       │         │
│           │                       │                       │         │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │   TimeWindow    │    │     Skill       │    │   Priority      │  │
│  │   (Concrete)    │    │   (Concrete)    │    │    (Enum)       │  │
│  │                 │    │                 │    │                 │  │
│  │ + start: dt     │    │ + id: str       │    │ + LOW           │  │
│  │ + end: dt       │    │ + level: enum   │    │ + ROUTINE       │  │
│  │ + timezone: str │    │ + parent_skills │    │ + HIGH          │  │
│  │                 │    │                 │    │ + URGENT        │  │
│  │ + to_timezone() │    │ + can_substitute│    │ + CRITICAL      │  │
│  │ + overlaps_with │    │                 │    │                 │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘

Inheritance Pattern:
    Resource (Abstract) - Supply Side
    ├── HealthcareResource (Medical skills, licenses)
    ├── EducationResource (Teaching subjects, grades)
    └── FieldServiceResource (Technical skills, tools)

    Task (Abstract) - Work Items
    ├── HealthcareTask (Patient visits, procedures)
    ├── EducationTask (Classes, lessons)
    └── FieldServiceTask (Service calls, repairs)

    Consumer (Abstract) - Demand Side
    ├── HealthcareConsumer (Patients with medical needs)
    ├── EducationConsumer (Students with learning needs)
    └── FieldServiceConsumer (Customers with service needs)

Organization:
1. Abstract Base Classes (Resource, Task, Consumer)
2. Enums (Priority, SkillLevel, LocationType)
3. Value Objects (Skill, TimeWindow, Location)
4. Helper Methods and Utilities
"""

import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
from zoneinfo import ZoneInfo


# ============================================================================
# 1. ABSTRACT BASE CLASSES (Core Framework - 80% Reusable)
# ============================================================================

@dataclass
class Resource(ABC):
    """
    Abstract base class for any entity that can perform tasks
    
    Examples by Domain:
    - Healthcare: Nurse, Doctor, Therapist, Medical Equipment
    - Education: Teacher, Professor, Classroom, Lab Equipment  
    - Field Service: Technician, Vehicle, Specialized Tools
    - Logistics: Driver, Vehicle, Warehouse Worker
    
    Core Attributes:
        id: Unique identifier for the resource
        name: Human-readable name
        skills: List of capabilities this resource possesses
        availability_windows: When this resource is available to work
        location: Base location of the resource
        priority: Preference ranking (1-10, higher = more preferred)
        max_daily_hours: Maximum hours this resource can work per day
        max_weekly_hours: Maximum hours this resource can work per week
    
    Real-Time Status Tracking:
        current_status: Current availability status
        unavailable_until: When resource becomes available again
        unavailability_reason: Why resource is currently unavailable
    """
    id: str
    name: str
    skills: List['Skill'] = field(default_factory=list)
    availability_windows: List['TimeWindow'] = field(default_factory=list)
    location: Optional['Location'] = None
    priority: int = 5  # 1-10 scale for resource preference
    max_daily_hours: int = 8
    max_weekly_hours: int = 40
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Real-time availability tracking (from TimeFold analysis)
    current_status: str = "AVAILABLE"  # AVAILABLE, SICK, UNAVAILABLE, ON_BREAK, EMERGENCY
    unavailable_until: Optional[datetime.datetime] = None
    unavailability_reason: str = ""
    last_status_change: Optional[datetime.datetime] = None
    
    @abstractmethod
    def can_perform_skill(self, required_skill: 'Skill') -> bool:
        """
        Check if this resource can perform the required skill
        
        Domain-specific implementations should override this to handle:
        - Skill hierarchies and substitution rules
        - Certification requirements
        - Equipment compatibility
        - License validation
        
        Args:
            required_skill: The skill that needs to be performed
            
        Returns:
            True if resource can perform the skill, False otherwise
        """
        pass
    
    def is_available_at(self, time: datetime.datetime) -> bool:
        """Check availability within time windows and current status"""
        # First check if resource is currently available
        if not self.is_currently_available():
            return False
        
        # Check if unavailable until a specific time
        if self.unavailable_until and time < self.unavailable_until:
            return False
        
        # Check availability windows
        for window in self.availability_windows:
            if window.start <= time <= window.end:
                return True
        return False
    
    def is_currently_available(self) -> bool:
        """Check if resource is currently available (not sick, etc.)"""
        return self.current_status == "AVAILABLE"
    
    def mark_sick(self, until: Optional[datetime.datetime] = None, reason: str = "illness"):
        """Mark resource as sick (from TimeFold real-time planning)"""
        self.current_status = "SICK"
        self.unavailable_until = until
        self.unavailability_reason = reason
        self.last_status_change = datetime.datetime.now()
    
    def mark_unavailable(self, until: Optional[datetime.datetime] = None, reason: str = "personal"):
        """Mark resource as unavailable (from TimeFold real-time planning)"""
        self.current_status = "UNAVAILABLE"
        self.unavailable_until = until
        self.unavailability_reason = reason
        self.last_status_change = datetime.datetime.now()
    
    def mark_available(self):
        """Mark resource as available again"""
        self.current_status = "AVAILABLE"
        self.unavailable_until = None
        self.unavailability_reason = ""
        self.last_status_change = datetime.datetime.now()


@dataclass
class Task(ABC):
    """
    Abstract base class for any work item that needs to be scheduled
    
    Examples by Domain:
    - Healthcare: Patient visit, Medical procedure, Assessment, Emergency response
    - Education: Class session, Exam, Lab session, Office hours
    - Field Service: Service call, Maintenance, Installation, Repair
    - Logistics: Delivery, Pickup, Warehouse operation, Route planning
    
    Core Attributes:
        id: Unique identifier for the task
        name: Human-readable description
        duration_minutes: How long the task takes to complete
        required_skills: Skills needed to perform this task
        preferred_skills: Skills that would be ideal but not required
        time_windows: When this task can be scheduled
        location: Where this task needs to be performed
        priority: How important this task is
        dependencies: Other tasks that must complete before this one
    
    Planning Variables (TimeFold optimizes these):
        assigned_resource: Which resource will perform this task
        start_time: When the task will begin
    
    Real-Time Features:
        Visit pinning: Lock assignments to prevent changes
        Duration extensions: Handle tasks that run longer than planned
        Status tracking: Monitor task lifecycle (scheduled → in progress → completed)
    """
    id: str
    name: str
    duration_minutes: int
    required_skills: List['Skill'] = field(default_factory=list)
    preferred_skills: List['Skill'] = field(default_factory=list)
    time_windows: List['TimeWindow'] = field(default_factory=list)
    location: Optional['Location'] = None
    priority: Optional['Priority'] = None
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Visit pinning support (from TimeFold analysis)
    is_pinned_assignment: bool = False  # If true, don't change assigned_resource
    is_pinned_time: bool = False  # If true, don't change start_time
    pinned_reason: str = ""  # Reason for pinning (e.g., "patient_request", "emergency")
    
    # Extended visits support (from TimeFold real-time planning analysis)
    original_duration_minutes: int = field(init=False, default=0)  # Store original duration
    duration_extension_minutes: int = 0  # Additional time beyond original
    extension_reason: str = ""  # Reason for extension
    
    # No-shows and cancellations support (from TimeFold real-time planning analysis)
    task_status: str = "SCHEDULED"  # SCHEDULED, COMPLETED, NO_SHOW, CANCELLED, IN_PROGRESS
    cancellation_reason: str = ""
    requires_rescheduling: bool = False
    no_show_time: Optional[datetime.datetime] = None
    
    def __post_init__(self):
        """Initialize original duration after object creation"""
        if self.original_duration_minutes == 0:
            object.__setattr__(self, 'original_duration_minutes', self.duration_minutes)
    
    @abstractmethod
    def get_end_time(self) -> Optional[datetime.datetime]:
        """
        Calculate end time based on start time and duration
        
        Domain-specific implementations should override this to handle:
        - Variable duration based on task complexity
        - Buffer time for setup/cleanup
        - Travel time considerations
        - Equipment preparation time
        
        Returns:
            End time if start_time is set, None otherwise
        """
        pass
    
    def get_total_duration_minutes(self) -> int:
        """Get total duration including extensions"""
        return self.duration_minutes + self.duration_extension_minutes

    def is_pinned(self) -> bool:
        """Check if this task should not be moved by the solver"""
        return self.is_pinned_assignment or self.is_pinned_time

    def pin_assignment(self, reason: str = "manual_pin"):
        """Pin the current assignment to prevent changes"""
        self.is_pinned_assignment = True
        self.pinned_reason = reason

    def pin_time(self, reason: str = "manual_pin"):
        """Pin the current time to prevent changes"""
        self.is_pinned_time = True
        self.pinned_reason = reason

    def unpin(self):
        """Remove all pinning constraints"""
        self.is_pinned_assignment = False
        self.is_pinned_time = False
        self.pinned_reason = ""

    def extend_task(self, additional_minutes: int, reason: str = ""):
        """Extend the task duration (from TimeFold real-time planning)"""
        self.duration_extension_minutes += additional_minutes
        self.extension_reason = reason

    def reset_to_original_duration(self):
        """Reset task to original planned duration"""
        self.duration_extension_minutes = 0
        self.extension_reason = ""

    def mark_no_show(self, reason: str = "", reschedule: bool = True):
        """Mark task as no-show (from TimeFold real-time planning)"""
        self.task_status = "NO_SHOW"
        self.cancellation_reason = reason
        self.requires_rescheduling = reschedule
        self.no_show_time = datetime.datetime.now()

    def cancel_task(self, reason: str = "", reschedule: bool = False):
        """Cancel the task (from TimeFold real-time planning)"""
        self.task_status = "CANCELLED"
        self.cancellation_reason = reason
        self.requires_rescheduling = reschedule

    def mark_completed(self):
        """Mark task as completed"""
        self.task_status = "COMPLETED"

    def is_active(self) -> bool:
        """Check if task is active (scheduled or in progress)"""
        return self.task_status in ["SCHEDULED", "IN_PROGRESS"]

    def needs_rescheduling(self) -> bool:
        """Check if task needs to be rescheduled"""
        return self.requires_rescheduling and self.task_status in ["NO_SHOW", "CANCELLED"]


@dataclass
class Consumer(ABC):
    """
    Abstract base class for any entity that consumes/requests services

    Examples by Domain:
    - Healthcare: Patient with medical conditions and care needs
    - Education: Student with learning requirements and academic goals
    - Field Service: Customer with equipment needing maintenance/repair
    - Logistics: Client with delivery/pickup requirements

    Core Attributes:
        id: Unique identifier for the consumer
        name: Human-readable name
        location: Primary location of the consumer
        preferences: Service preferences and requirements
        constraints: Limitations or restrictions for service delivery
        priority_level: How important this consumer is (VIP, standard, etc.)
        contact_info: How to reach the consumer
        service_history: Past interactions and service records

    Relationship to Tasks:
        A Consumer generates Tasks that need Resources to fulfill them
        Example: Patient (Consumer) needs wound care (Task) from Nurse (Resource)

    Consumer Lifecycle:
        Active: Currently receiving services
        Inactive: Not currently receiving services but may in future
        Suspended: Temporarily not receiving services
        Discharged: No longer receiving services
    """
    id: str
    name: str
    location: Optional['Location'] = None
    preferences: Dict[str, Any] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)
    priority_level: int = 3  # 1-5 scale (1=low, 5=VIP)
    contact_info: Dict[str, str] = field(default_factory=dict)
    service_history: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Consumer status tracking
    status: str = "ACTIVE"  # ACTIVE, INACTIVE, SUSPENDED, DISCHARGED
    status_reason: str = ""
    last_service_date: Optional[datetime.datetime] = None
    next_service_due: Optional[datetime.datetime] = None

    @abstractmethod
    def get_service_requirements(self) -> List[str]:
        """
        Get list of service requirements for this consumer

        Domain-specific implementations should override this to return:
        - Healthcare: Medical conditions, care plans, medication schedules
        - Education: Learning objectives, accommodation needs, curriculum requirements
        - Field Service: Equipment types, maintenance schedules, warranty status
        - Logistics: Delivery preferences, pickup schedules, special handling

        Returns:
            List of service requirement identifiers
        """
        pass

    @abstractmethod
    def can_receive_service_at(self, time: datetime.datetime) -> bool:
        """
        Check if consumer can receive service at given time

        Domain-specific implementations should consider:
        - Healthcare: Patient availability, medical restrictions, family schedules
        - Education: Class schedules, extracurricular activities, study periods
        - Field Service: Business hours, equipment downtime windows, staff availability
        - Logistics: Receiving hours, loading dock availability, staff presence

        Args:
            time: When service would be provided

        Returns:
            True if consumer can receive service at this time
        """
        pass

    def is_active(self) -> bool:
        """Check if consumer is currently active for service"""
        return self.status == "ACTIVE"

    def get_preferred_time_windows(self) -> List['TimeWindow']:
        """Get consumer's preferred service time windows"""
        # Extract from preferences if available
        windows = []
        if 'preferred_times' in self.preferences:
            # Implementation would parse preference data into TimeWindow objects
            pass
        return windows

    def add_service_record(self, service_description: str, timestamp: Optional[datetime.datetime] = None):
        """Add a service record to consumer's history"""
        if timestamp is None:
            timestamp = datetime.datetime.now()

        record = f"{timestamp.isoformat()}: {service_description}"
        self.service_history.append(record)
        self.last_service_date = timestamp

    def suspend_service(self, reason: str = "", until: Optional[datetime.datetime] = None):
        """Temporarily suspend service for this consumer"""
        self.status = "SUSPENDED"
        self.status_reason = reason
        if until:
            self.next_service_due = until

    def reactivate_service(self):
        """Reactivate service for this consumer"""
        self.status = "ACTIVE"
        self.status_reason = ""

    def discharge_consumer(self, reason: str = ""):
        """Permanently discharge consumer from service"""
        self.status = "DISCHARGED"
        self.status_reason = reason


# ============================================================================
# 2. ENUMS (Core Value Types)
# ============================================================================

class SkillLevel(Enum):
    """Skill proficiency levels for hierarchical skill matching"""
    BASIC = "BASIC"
    INTERMEDIATE = "INTERMEDIATE"
    ADVANCED = "ADVANCED"
    EXPERT = "EXPERT"


class Priority(Enum):
    """Task priority levels"""
    LOW = 1
    ROUTINE = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class LocationType(Enum):
    """Types of locations where tasks can be performed"""
    OFFICE = "OFFICE"
    REMOTE = "REMOTE"
    CLIENT_SITE = "CLIENT_SITE"
    MOBILE = "MOBILE"


# ============================================================================
# 3. VALUE OBJECTS (Core Data Structures)
# ============================================================================

@dataclass(frozen=True)
class Skill:
    """
    Represents a capability or competency required to perform tasks
    Supports hierarchical relationships for skill substitution

    Hierarchy Example:
        WOUND_CARE_BASIC
        └── WOUND_CARE_ADVANCED (can substitute for basic)
            └── WOUND_CARE_SPECIALIST (can substitute for both)

    Attributes:
        id: Unique skill identifier
        name: Human-readable skill name
        level: Proficiency level (BASIC, INTERMEDIATE, ADVANCED, EXPERT)
        parent_skills: Skills this skill can substitute for
        description: Optional detailed description
    """
    id: str
    name: str
    level: SkillLevel
    parent_skills: List[str] = field(default_factory=list)
    description: str = ""

    def can_substitute_for(self, other_skill: 'Skill') -> bool:
        """Check if this skill can substitute for another skill"""
        # Same skill
        if self.id == other_skill.id:
            return True

        # Higher level of same skill family
        if other_skill.id in self.parent_skills:
            return True

        # Check skill level hierarchy
        if self.level.value >= other_skill.level.value:
            return any(parent in self.parent_skills for parent in other_skill.parent_skills)

        return False


@dataclass(frozen=True)
class TimeWindow:
    """
    Flexible time window representation with timezone support
    Used for availability windows, task time constraints, etc.

    Features:
    - Timezone conversion and DST handling
    - Overlap detection across timezones
    - Flexibility for schedule adjustments

    Attributes:
        start: Start time of the window
        end: End time of the window
        timezone: Timezone identifier (e.g., "America/New_York")
        is_preferred: Whether this is a preferred time window
        flexibility_minutes: How much the window can shift
    """
    start: datetime.datetime
    end: datetime.datetime
    timezone: str = "UTC"
    is_preferred: bool = False
    flexibility_minutes: int = 0  # How much the window can shift

    def to_timezone(self, target_timezone: str) -> 'TimeWindow':
        """Convert time window to different timezone"""
        source_tz = ZoneInfo(self.timezone)
        target_tz = ZoneInfo(target_timezone)

        # Ensure datetime objects are timezone-aware
        start_aware = self.start.replace(tzinfo=source_tz) if self.start.tzinfo is None else self.start
        end_aware = self.end.replace(tzinfo=source_tz) if self.end.tzinfo is None else self.end

        # Convert to target timezone
        start_converted = start_aware.astimezone(target_tz)
        end_converted = end_aware.astimezone(target_tz)

        return TimeWindow(
            start=start_converted.replace(tzinfo=None),  # Store as naive datetime
            end=end_converted.replace(tzinfo=None),
            timezone=target_timezone,
            is_preferred=self.is_preferred,
            flexibility_minutes=self.flexibility_minutes
        )

    def overlaps_with(self, other: 'TimeWindow', convert_timezone: bool = True) -> bool:
        """Check if this time window overlaps with another, handling timezone conversion"""
        other_window = other

        if convert_timezone and self.timezone != other.timezone:
            # Convert other window to our timezone for comparison
            other_window = other.to_timezone(self.timezone)

        return not (self.end <= other_window.start or other_window.end <= self.start)

    def get_duration_minutes(self) -> int:
        """Get duration of time window in minutes"""
        return int((self.end - self.start).total_seconds() / 60)


@dataclass
class Location:
    """
    Represents a physical or virtual location where tasks can be performed
    Supports geographic coordinates for routing and distance calculations

    Features:
    - Geographic coordinates for routing
    - Safety ratings for risk assessment
    - Accessibility features tracking
    - Timezone support for multi-region operations

    Attributes:
        id: Unique location identifier
        name: Human-readable location name
        latitude/longitude: GPS coordinates for routing
        location_type: Type of location (office, client site, etc.)
        address: Full street address
        timezone: Local timezone
        accessibility_features: List of accessibility accommodations
        safety_rating: Safety score (1-5, 5 being safest)
        parking_available: Whether parking is available
        metadata: Additional location-specific data
    """
    id: str
    name: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location_type: LocationType = LocationType.OFFICE
    address: str = ""
    timezone: str = "UTC"
    accessibility_features: List[str] = field(default_factory=list)
    safety_rating: int = 5  # 1-5 scale, 5 being safest
    parking_available: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

    def distance_to(self, other: 'Location') -> Optional[float]:
        """Calculate distance to another location (if coordinates available)"""
        if (self.latitude is None or self.longitude is None or
            other.latitude is None or other.longitude is None):
            return None

        # Haversine formula for great circle distance
        import math

        lat1, lon1 = math.radians(self.latitude), math.radians(self.longitude)
        lat2, lon2 = math.radians(other.latitude), math.radians(other.longitude)

        dlat = lat2 - lat1
        dlon = lon2 - lon1

        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # Earth's radius in kilometers
        earth_radius_km = 6371
        return earth_radius_km * c
