"""
Core Domain Model - Abstract Base Classes for Domain-Agnostic Scheduling
========================================================================

This module contains the abstract base classes that define the core scheduling
concepts. These classes are designed to be inherited by domain-specific
implementations (healthcare, education, field service, etc.).

Key Concepts:
- Resource: Abstract entity that can perform tasks (nurse, teacher, technician)
- Task: Abstract work item that needs to be scheduled (patient visit, class, service call)
- Location: Physical or virtual place where tasks are performed
- TimeWindow: Time constraints for availability and scheduling
- Skill: Capabilities required to perform tasks
"""

import datetime
from typing import List, Set, Optional, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import pytz
from zoneinfo import ZoneInfo


# ============================================================================
# Core Enums and Value Objects
# ============================================================================

class SkillLevel(Enum):
    """Skill proficiency levels for hierarchical skill matching"""
    BASIC = "BASIC"
    INTERMEDIATE = "INTERMEDIATE"
    ADVANCED = "ADVANCED"
    EXPERT = "EXPERT"


class Priority(Enum):
    """Task priority levels"""
    LOW = 1
    ROUTINE = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class LocationType(Enum):
    """Types of locations where tasks can be performed"""
    OFFICE = "OFFICE"
    REMOTE = "REMOTE"
    CLIENT_SITE = "CLIENT_SITE"
    MOBILE = "MOBILE"


@dataclass(frozen=True)
class Skill:
    """
    Represents a capability or competency required to perform tasks
    Supports hierarchical relationships for skill substitution
    """
    id: str
    name: str
    level: SkillLevel
    parent_skills: List[str] = field(default_factory=list)
    description: str = ""
    
    def can_substitute_for(self, other_skill: 'Skill') -> bool:
        """Check if this skill can substitute for another skill"""
        # Same skill
        if self.id == other_skill.id:
            return True
        
        # Higher level of same skill family
        if other_skill.id in self.parent_skills:
            return True
        
        # Check skill level hierarchy
        if self.level.value >= other_skill.level.value:
            return any(parent in self.parent_skills for parent in other_skill.parent_skills)
        
        return False


@dataclass(frozen=True)
class TimeWindow:
    """
    Flexible time window representation with timezone support
    Used for availability windows, task time constraints, etc.
    """
    start: datetime.datetime
    end: datetime.datetime
    timezone: str = "UTC"
    is_preferred: bool = False
    flexibility_minutes: int = 0  # How much the window can shift
    
    def to_timezone(self, target_timezone: str) -> 'TimeWindow':
        """Convert time window to different timezone"""
        source_tz = ZoneInfo(self.timezone)
        target_tz = ZoneInfo(target_timezone)
        
        # Ensure datetime objects are timezone-aware
        start_aware = self.start.replace(tzinfo=source_tz) if self.start.tzinfo is None else self.start
        end_aware = self.end.replace(tzinfo=source_tz) if self.end.tzinfo is None else self.end
        
        # Convert to target timezone
        start_converted = start_aware.astimezone(target_tz)
        end_converted = end_aware.astimezone(target_tz)
        
        return TimeWindow(
            start=start_converted.replace(tzinfo=None),  # Store as naive datetime
            end=end_converted.replace(tzinfo=None),
            timezone=target_timezone,
            is_preferred=self.is_preferred,
            flexibility_minutes=self.flexibility_minutes
        )
    
    def overlaps_with(self, other: 'TimeWindow', convert_timezone: bool = True) -> bool:
        """Check if this time window overlaps with another, handling timezone conversion"""
        other_window = other
        
        if convert_timezone and self.timezone != other.timezone:
            # Convert other window to our timezone for comparison
            other_window = other.to_timezone(self.timezone)
        
        return not (self.end <= other_window.start or other_window.end <= self.start)
    
    def get_duration_minutes(self) -> int:
        """Get duration of time window in minutes"""
        return int((self.end - self.start).total_seconds() / 60)


@dataclass
class Location:
    """
    Represents a physical or virtual location where tasks can be performed
    Supports geographic coordinates for routing and distance calculations
    """
    id: str
    name: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location_type: LocationType = LocationType.OFFICE
    address: str = ""
    timezone: str = "UTC"
    accessibility_features: List[str] = field(default_factory=list)
    safety_rating: int = 5  # 1-5 scale, 5 being safest
    parking_available: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def distance_to(self, other: 'Location') -> Optional[float]:
        """Calculate distance to another location (if coordinates available)"""
        if (self.latitude is None or self.longitude is None or
            other.latitude is None or other.longitude is None):
            return None
        
        # Haversine formula for great circle distance
        import math
        
        lat1, lon1 = math.radians(self.latitude), math.radians(self.longitude)
        lat2, lon2 = math.radians(other.latitude), math.radians(other.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth's radius in kilometers
        earth_radius_km = 6371
        return earth_radius_km * c


# ============================================================================
# Abstract Base Classes
# ============================================================================

@dataclass
class Resource(ABC):
    """
    Abstract base class for any entity that can perform tasks
    Examples: Nurse, Teacher, Technician, Vehicle, Equipment
    """
    id: str
    name: str
    skills: List[Skill] = field(default_factory=list)
    availability_windows: List[TimeWindow] = field(default_factory=list)
    location: Optional[Location] = None
    priority: int = 5  # 1-10 scale for resource preference
    max_daily_hours: int = 8
    max_weekly_hours: int = 40
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Staff availability and illness tracking (from TimeFold real-time planning analysis)
    current_status: str = "AVAILABLE"  # AVAILABLE, SICK, UNAVAILABLE, ON_BREAK, EMERGENCY
    unavailable_until: Optional[datetime.datetime] = None
    unavailability_reason: str = ""
    last_status_change: Optional[datetime.datetime] = None
    
    @abstractmethod
    def can_perform_skill(self, required_skill: Skill) -> bool:
        """Check if this resource can perform the required skill"""
        pass
    
    def is_available_at(self, time: datetime.datetime) -> bool:
        """Check availability within time windows and current status"""
        # First check if resource is currently available
        if not self.is_currently_available():
            return False
        
        # Check if unavailable until a specific time
        if self.unavailable_until and time < self.unavailable_until:
            return False
        
        # Check availability windows
        for window in self.availability_windows:
            if window.start <= time <= window.end:
                return True
        return False
    
    def is_currently_available(self) -> bool:
        """Check if resource is currently available (not sick, etc.)"""
        return self.current_status == "AVAILABLE"
    
    def mark_sick(self, until: Optional[datetime.datetime] = None, reason: str = "illness"):
        """Mark resource as sick (from TimeFold real-time planning)"""
        self.current_status = "SICK"
        self.unavailable_until = until
        self.unavailability_reason = reason
        self.last_status_change = datetime.datetime.now()
    
    def mark_unavailable(self, until: Optional[datetime.datetime] = None, reason: str = "personal"):
        """Mark resource as unavailable (from TimeFold real-time planning)"""
        self.current_status = "UNAVAILABLE"
        self.unavailable_until = until
        self.unavailability_reason = reason
        self.last_status_change = datetime.datetime.now()
    
    def mark_available(self):
        """Mark resource as available again"""
        self.current_status = "AVAILABLE"
        self.unavailable_until = None
        self.unavailability_reason = ""
        self.last_status_change = datetime.datetime.now()


@dataclass
class Task(ABC):
    """
    Abstract base class for any work item that needs to be scheduled
    Examples: Patient visit, Class session, Service call, Delivery
    """
    id: str
    name: str
    duration_minutes: int
    required_skills: List[Skill] = field(default_factory=list)
    preferred_skills: List[Skill] = field(default_factory=list)
    time_windows: List[TimeWindow] = field(default_factory=list)
    location: Optional[Location] = None
    priority: Priority = Priority.ROUTINE
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Visit pinning support (from TimeFold analysis)
    is_pinned_assignment: bool = False  # If true, don't change assigned_resource
    is_pinned_time: bool = False  # If true, don't change start_time
    pinned_reason: str = ""  # Reason for pinning (e.g., "patient_request", "emergency")
    
    # Extended visits support (from TimeFold real-time planning analysis)
    original_duration_minutes: int = field(init=False)  # Store original duration
    duration_extension_minutes: int = 0  # Additional time beyond original
    extension_reason: str = ""  # Reason for extension
    
    # No-shows and cancellations support (from TimeFold real-time planning analysis)
    task_status: str = "SCHEDULED"  # SCHEDULED, COMPLETED, NO_SHOW, CANCELLED, IN_PROGRESS
    cancellation_reason: str = ""
    requires_rescheduling: bool = False
    no_show_time: Optional[datetime.datetime] = None
    
    def __post_init__(self):
        """Initialize original duration after object creation"""
        if not hasattr(self, 'original_duration_minutes') or self.original_duration_minutes == 0:
            object.__setattr__(self, 'original_duration_minutes', self.duration_minutes)
    
    @abstractmethod
    def get_end_time(self) -> Optional[datetime.datetime]:
        """Calculate end time based on start time and duration"""
        pass
    
    def get_total_duration_minutes(self) -> int:
        """Get total duration including extensions"""
        return self.duration_minutes + self.duration_extension_minutes
    
    def is_pinned(self) -> bool:
        """Check if this task should not be moved by the solver"""
        return self.is_pinned_assignment or self.is_pinned_time
    
    def pin_assignment(self, reason: str = "manual_pin"):
        """Pin the current assignment to prevent changes"""
        self.is_pinned_assignment = True
        self.pinned_reason = reason
    
    def pin_time(self, reason: str = "manual_pin"):
        """Pin the current time to prevent changes"""
        self.is_pinned_time = True
        self.pinned_reason = reason
    
    def unpin(self):
        """Remove all pinning constraints"""
        self.is_pinned_assignment = False
        self.is_pinned_time = False
        self.pinned_reason = ""
    
    def extend_task(self, additional_minutes: int, reason: str = ""):
        """Extend the task duration (from TimeFold real-time planning)"""
        self.duration_extension_minutes += additional_minutes
        self.extension_reason = reason
    
    def reset_to_original_duration(self):
        """Reset task to original planned duration"""
        self.duration_extension_minutes = 0
        self.extension_reason = ""
    
    def mark_no_show(self, reason: str = "", reschedule: bool = True):
        """Mark task as no-show (from TimeFold real-time planning)"""
        self.task_status = "NO_SHOW"
        self.cancellation_reason = reason
        self.requires_rescheduling = reschedule
        self.no_show_time = datetime.datetime.now()
    
    def cancel_task(self, reason: str = "", reschedule: bool = False):
        """Cancel the task (from TimeFold real-time planning)"""
        self.task_status = "CANCELLED"
        self.cancellation_reason = reason
        self.requires_rescheduling = reschedule
    
    def mark_completed(self):
        """Mark task as completed"""
        self.task_status = "COMPLETED"
    
    def is_active(self) -> bool:
        """Check if task is active (scheduled or in progress)"""
        return self.task_status in ["SCHEDULED", "IN_PROGRESS"]
    
    def needs_rescheduling(self) -> bool:
        """Check if task needs to be rescheduled"""
        return self.requires_rescheduling and self.task_status in ["NO_SHOW", "CANCELLED"]
