"""
Healthcare Integrations - External Service Integrations
=======================================================

This module provides integrations with external services for healthcare
scheduling, including Google Maps for travel optimization, EHR systems,
and other healthcare-specific APIs.

Key Integrations:
- GoogleMapsHealthcareService: Travel time and route optimization
- EHRIntegration: Electronic Health Record system integration (future)
- NotificationService: Patient and staff notifications (future)
"""

import datetime
import asyncio
from typing import List, Optional, Dict, Any, Tuple
import googlemaps
import aiohttp

from core.domain import Location


class GoogleMapsHealthcareService:
    """
    Google Maps integration specialized for healthcare scheduling
    Provides travel time calculations and route optimization for home healthcare
    """
    
    def __init__(self, api_key: str):
        """
        Initialize Google Maps service
        
        Args:
            api_key: Google Maps API key
        """
        self.client = googlemaps.Client(key=api_key)
        self.api_key = api_key
        
        # Healthcare-specific configuration
        self.default_travel_mode = "driving"
        self.traffic_model = "best_guess"
        self.fuel_cost_per_km = 0.15  # USD per km (configurable)
        
    def get_travel_info(self, origin: Location, destination: Location,
                       departure_time: Optional[datetime.datetime] = None) -> Dict[str, Any]:
        """
        Get travel information between two locations
        
        Args:
            origin: Starting location
            destination: Ending location
            departure_time: When travel will occur (for traffic data)
            
        Returns:
            Dictionary with travel information
        """
        try:
            # Prepare coordinates
            origin_coords = (origin.latitude, origin.longitude)
            destination_coords = (destination.latitude, destination.longitude)
            
            # Use current time if no departure time specified
            if departure_time is None:
                departure_time = datetime.datetime.now()
            
            # Get directions with traffic data
            directions = self.client.directions(
                origin=origin_coords,
                destination=destination_coords,
                mode=self.default_travel_mode,
                departure_time=departure_time,
                traffic_model=self.traffic_model
            )
            
            if not directions:
                return self._create_fallback_travel_info(origin, destination)
            
            # Extract travel information
            route = directions[0]
            leg = route['legs'][0]
            
            # Get duration in traffic if available
            duration_seconds = leg.get('duration_in_traffic', leg['duration'])['value']
            distance_meters = leg['distance']['value']
            
            return {
                'duration_seconds': duration_seconds,
                'duration_minutes': duration_seconds / 60,
                'distance_meters': distance_meters,
                'distance_km': distance_meters / 1000,
                'estimated_fuel_cost': (distance_meters / 1000) * self.fuel_cost_per_km,
                'traffic_delay_seconds': duration_seconds - leg['duration']['value'],
                'route_polyline': route['overview_polyline']['points'],
                'start_address': leg['start_address'],
                'end_address': leg['end_address']
            }
            
        except Exception as e:
            print(f"Google Maps API error: {e}")
            return self._create_fallback_travel_info(origin, destination)
    
    def get_travel_matrix(self, origins: List[Location], 
                         destinations: List[Location],
                         departure_time: Optional[datetime.datetime] = None) -> Dict[str, Any]:
        """
        Get travel matrix for multiple origins and destinations
        Useful for bulk route optimization
        
        Args:
            origins: List of origin locations
            destinations: List of destination locations
            departure_time: When travel will occur
            
        Returns:
            Travel matrix with all combinations
        """
        try:
            # Prepare coordinate lists
            origin_coords = [(loc.latitude, loc.longitude) for loc in origins]
            dest_coords = [(loc.latitude, loc.longitude) for loc in destinations]
            
            if departure_time is None:
                departure_time = datetime.datetime.now()
            
            # Get distance matrix
            matrix = self.client.distance_matrix(
                origins=origin_coords,
                destinations=dest_coords,
                mode=self.default_travel_mode,
                departure_time=departure_time,
                traffic_model=self.traffic_model
            )
            
            # Process results
            travel_matrix = {}
            for i, origin in enumerate(origins):
                travel_matrix[origin.id] = {}
                for j, destination in enumerate(destinations):
                    element = matrix['rows'][i]['elements'][j]
                    
                    if element['status'] == 'OK':
                        duration_seconds = element.get('duration_in_traffic', element['duration'])['value']
                        distance_meters = element['distance']['value']
                        
                        travel_matrix[origin.id][destination.id] = {
                            'duration_seconds': duration_seconds,
                            'duration_minutes': duration_seconds / 60,
                            'distance_meters': distance_meters,
                            'distance_km': distance_meters / 1000,
                            'estimated_fuel_cost': (distance_meters / 1000) * self.fuel_cost_per_km
                        }
                    else:
                        # Fallback for failed requests
                        fallback = self._create_fallback_travel_info(origin, destination)
                        travel_matrix[origin.id][destination.id] = fallback
            
            return {
                'matrix': travel_matrix,
                'origins': [loc.id for loc in origins],
                'destinations': [loc.id for loc in destinations],
                'departure_time': departure_time.isoformat()
            }
            
        except Exception as e:
            print(f"Google Maps Matrix API error: {e}")
            return self._create_fallback_matrix(origins, destinations)
    
    def optimize_healthcare_route(self, start_location: Location,
                                patient_locations: List[Location],
                                end_location: Optional[Location] = None) -> Dict[str, Any]:
        """
        Optimize route through multiple patient home locations
        
        Args:
            start_location: Starting point (usually agency office)
            patient_locations: List of patient home locations to visit
            end_location: Ending point (usually agency office or last patient)
            
        Returns:
            Optimized route information
        """
        if not patient_locations:
            return {}
        
        if end_location is None:
            end_location = start_location
        
        try:
            # Prepare waypoints (patient homes)
            waypoints = [(loc.latitude, loc.longitude) for loc in patient_locations]
            
            # Get optimized route
            directions = self.client.directions(
                origin=(start_location.latitude, start_location.longitude),
                destination=(end_location.latitude, end_location.longitude),
                waypoints=waypoints,
                optimize_waypoints=True,
                mode=self.default_travel_mode,
                departure_time=datetime.datetime.now(),
                traffic_model=self.traffic_model
            )
            
            if not directions:
                return self._create_fallback_route_info(start_location, patient_locations, end_location)
            
            route = directions[0]
            
            # Calculate total distance and duration
            total_distance_meters = 0
            total_duration_seconds = 0
            
            for leg in route['legs']:
                total_distance_meters += leg['distance']['value']
                duration = leg.get('duration_in_traffic', leg['duration'])['value']
                total_duration_seconds += duration
            
            # Get optimized waypoint order
            waypoint_order = route.get('waypoint_order', list(range(len(patient_locations))))
            optimized_patient_order = [patient_locations[i] for i in waypoint_order]
            
            return {
                'total_distance_km': total_distance_meters / 1000,
                'total_duration_hours': total_duration_seconds / 3600,
                'total_duration_minutes': total_duration_seconds / 60,
                'estimated_fuel_cost': (total_distance_meters / 1000) * self.fuel_cost_per_km,
                'optimized_patient_order': [loc.id for loc in optimized_patient_order],
                'route_polyline': route['overview_polyline']['points'],
                'waypoint_order': waypoint_order,
                'legs': [
                    {
                        'start_address': leg['start_address'],
                        'end_address': leg['end_address'],
                        'distance_km': leg['distance']['value'] / 1000,
                        'duration_minutes': leg.get('duration_in_traffic', leg['duration'])['value'] / 60
                    }
                    for leg in route['legs']
                ]
            }
            
        except Exception as e:
            print(f"Route optimization error: {e}")
            return self._create_fallback_route_info(start_location, patient_locations, end_location)
    
    def get_real_time_traffic_delay(self, origin: Location, destination: Location) -> int:
        """
        Get current traffic delay in minutes
        
        Args:
            origin: Starting location
            destination: Ending location
            
        Returns:
            Traffic delay in minutes
        """
        travel_info = self.get_travel_info(origin, destination)
        return int(travel_info.get('traffic_delay_seconds', 0) / 60)
    
    def estimate_arrival_time(self, origin: Location, destination: Location,
                            departure_time: datetime.datetime) -> datetime.datetime:
        """
        Estimate arrival time considering traffic
        
        Args:
            origin: Starting location
            destination: Ending location
            departure_time: When travel starts
            
        Returns:
            Estimated arrival time
        """
        travel_info = self.get_travel_info(origin, destination, departure_time)
        travel_duration = datetime.timedelta(seconds=travel_info['duration_seconds'])
        return departure_time + travel_duration
    
    # ========================================================================
    # Helper Methods
    # ========================================================================
    
    def _create_fallback_travel_info(self, origin: Location, destination: Location) -> Dict[str, Any]:
        """Create fallback travel info when API fails"""
        # Use straight-line distance as fallback
        distance_km = origin.distance_to(destination) or 10.0  # Default 10km
        
        # Estimate travel time (assuming 40 km/h average speed)
        duration_minutes = (distance_km / 40) * 60
        
        return {
            'duration_seconds': int(duration_minutes * 60),
            'duration_minutes': duration_minutes,
            'distance_meters': int(distance_km * 1000),
            'distance_km': distance_km,
            'estimated_fuel_cost': distance_km * self.fuel_cost_per_km,
            'traffic_delay_seconds': 0,
            'route_polyline': '',
            'start_address': origin.address or origin.name,
            'end_address': destination.address or destination.name,
            'fallback': True
        }
    
    def _create_fallback_matrix(self, origins: List[Location], 
                              destinations: List[Location]) -> Dict[str, Any]:
        """Create fallback travel matrix when API fails"""
        matrix = {}
        for origin in origins:
            matrix[origin.id] = {}
            for destination in destinations:
                fallback_info = self._create_fallback_travel_info(origin, destination)
                matrix[origin.id][destination.id] = fallback_info
        
        return {
            'matrix': matrix,
            'origins': [loc.id for loc in origins],
            'destinations': [loc.id for loc in destinations],
            'departure_time': datetime.datetime.now().isoformat(),
            'fallback': True
        }
    
    def _create_fallback_route_info(self, start_location: Location,
                                  patient_locations: List[Location],
                                  end_location: Location) -> Dict[str, Any]:
        """Create fallback route info when optimization fails"""
        # Simple route: start -> patients in order -> end
        total_distance = 0.0
        
        current_location = start_location
        for patient_location in patient_locations:
            distance = current_location.distance_to(patient_location) or 5.0
            total_distance += distance
            current_location = patient_location
        
        # Add distance to end location
        if end_location != current_location:
            total_distance += current_location.distance_to(end_location) or 5.0
        
        # Estimate duration (40 km/h average)
        total_duration_hours = total_distance / 40
        
        return {
            'total_distance_km': total_distance,
            'total_duration_hours': total_duration_hours,
            'total_duration_minutes': total_duration_hours * 60,
            'estimated_fuel_cost': total_distance * self.fuel_cost_per_km,
            'optimized_patient_order': [loc.id for loc in patient_locations],
            'route_polyline': '',
            'waypoint_order': list(range(len(patient_locations))),
            'legs': [],
            'fallback': True
        }
