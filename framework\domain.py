"""
Domain-Agnostic Scheduling Framework - Abstract Domain Model

This module defines abstract base classes and interfaces for a flexible scheduling
framework that can be applied to any domain (healthcare, delivery, manufacturing, etc.).

The framework follows a provider-consumer model where:
- Resources (providers) have capabilities and availability
- Tasks (consumers) require capabilities and have time constraints
- The scheduler optimizes assignment of tasks to resources

Key Design Principles:
1. Separation of Concerns: Core scheduling logic is separate from domain specifics
2. Type Safety: Generic types ensure compile-time checking
3. Extensibility: Easy to add new domains without modifying core components
4. SOLID Principles: Single responsibility, open/closed, dependency inversion
"""

import datetime
from abc import ABC, abstractmethod
from typing import List, Set, Optional, TypeVar, Generic, Any, Dict
from dataclasses import dataclass, field
from timefold.solver.domain import planning_entity, planning_solution
from timefold.solver.domain import PlanningVariable, PlanningId, ValueRangeProvider
from timefold.solver.domain import ProblemFactCollectionProperty, PlanningEntityCollectionProperty, PlanningScore
from timefold.solver.score import HardSoftScore

# Type variables for generic constraints
ResourceType = TypeVar('ResourceType', bound='AbstractResource')
TaskType = TypeVar('TaskType', bound='AbstractTask')
CapabilityType = TypeVar('CapabilityType', bound='AbstractCapability')
LocationType = TypeVar('LocationType', bound='AbstractLocation')


class AbstractCapability(ABC):
    """
    Abstract base class for capabilities/skills that resources can have
    and tasks can require.
    
    Examples:
    - Healthcare: Nursing skills, therapy specializations
    - Delivery: Vehicle types, driver licenses
    - Manufacturing: Machine capabilities, operator certifications
    """
    
    @abstractmethod
    def get_identifier(self) -> str:
        """Return unique identifier for this capability"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other: 'AbstractCapability') -> bool:
        """Check if this capability is compatible with another"""
        pass
    
    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.get_identifier() == other.get_identifier()
    
    def __hash__(self):
        return hash(self.get_identifier())


class AbstractLocation(ABC):
    """
    Abstract base class for locations/zones where tasks occur
    and resources can serve.
    
    Examples:
    - Healthcare: Service areas, hospital wings
    - Delivery: Geographic zones, postal codes
    - Manufacturing: Production lines, factory floors
    """
    
    @abstractmethod
    def get_identifier(self) -> str:
        """Return unique identifier for this location"""
        pass
    
    @abstractmethod
    def is_accessible_from(self, other: 'AbstractLocation') -> bool:
        """Check if this location is accessible from another location"""
        pass
    
    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.get_identifier() == other.get_identifier()
    
    def __hash__(self):
        return hash(self.get_identifier())


class AbstractTimeSlot(ABC):
    """
    Abstract base class for time availability slots.
    
    Represents a continuous period when a resource is available
    or when a task can be scheduled.
    """
    
    @abstractmethod
    def get_start(self) -> datetime.datetime:
        """Return start time of this slot"""
        pass
    
    @abstractmethod
    def get_end(self) -> datetime.datetime:
        """Return end time of this slot"""
        pass
    
    @abstractmethod
    def contains_range(self, start: datetime.datetime, end: datetime.datetime) -> bool:
        """Check if this slot completely contains the given time range"""
        pass
    
    @abstractmethod
    def overlaps_with(self, other: 'AbstractTimeSlot') -> bool:
        """Check if this slot overlaps with another time slot"""
        pass


class AbstractDependency(ABC):
    """
    Abstract base class for task dependencies.
    
    Represents relationships between tasks that affect scheduling order
    and resource assignment.
    """
    
    @abstractmethod
    def get_predecessor_id(self) -> Any:
        """Return identifier of the predecessor task"""
        pass
    
    @abstractmethod
    def requires_same_resource(self) -> bool:
        """Return True if dependent tasks must use the same resource"""
        pass
    
    @abstractmethod
    def get_minimum_gap(self) -> datetime.timedelta:
        """Return minimum time gap required between predecessor and successor"""
        pass


class AbstractResource(ABC):
    """
    Abstract base class for resources (providers) in the scheduling system.
    
    Resources have capabilities, serve locations, have availability windows,
    and capacity constraints.
    
    Examples:
    - Healthcare: Clinicians with skills and service areas
    - Delivery: Drivers with vehicles and route zones  
    - Manufacturing: Machines with capabilities and operators
    """
    
    @abstractmethod
    def get_id(self) -> Any:
        """Return unique identifier for this resource"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Return human-readable name for this resource"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> Set[AbstractCapability]:
        """Return set of capabilities this resource has"""
        pass
    
    @abstractmethod
    def get_locations(self) -> Set[AbstractLocation]:
        """Return set of locations this resource can serve"""
        pass
    
    @abstractmethod
    def get_availability(self) -> List[AbstractTimeSlot]:
        """Return list of time slots when this resource is available"""
        pass
    
    @abstractmethod
    def get_capacity_limit(self) -> int:
        """Return maximum number of tasks this resource can handle"""
        pass
    
    @abstractmethod
    def can_handle_task(self, task: 'AbstractTask') -> bool:
        """Check if this resource can handle the given task"""
        pass
    
    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.get_id() == other.get_id()
    
    def __hash__(self):
        return hash(self.get_id())


class AbstractTask(ABC):
    """
    Abstract base class for tasks (consumers) in the scheduling system.
    
    Tasks require capabilities, occur at locations, have time windows,
    duration, and may have dependencies on other tasks.
    
    Examples:
    - Healthcare: Patient visits requiring specific skills
    - Delivery: Package deliveries with time windows
    - Manufacturing: Production jobs requiring specific machines
    """
    
    @abstractmethod
    def get_id(self) -> Any:
        """Return unique identifier for this task"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Return human-readable name for this task"""
        pass
    
    @abstractmethod
    def get_required_capability(self) -> AbstractCapability:
        """Return capability required to perform this task"""
        pass
    
    @abstractmethod
    def get_location(self) -> AbstractLocation:
        """Return location where this task must be performed"""
        pass
    
    @abstractmethod
    def get_time_window_start(self) -> datetime.datetime:
        """Return earliest time this task can start"""
        pass
    
    @abstractmethod
    def get_time_window_end(self) -> datetime.datetime:
        """Return latest time this task can end"""
        pass
    
    @abstractmethod
    def get_duration(self) -> datetime.timedelta:
        """Return duration required to complete this task"""
        pass
    
    @abstractmethod
    def is_fixed_time(self) -> bool:
        """Return True if this task has a fixed start time"""
        pass
    
    @abstractmethod
    def get_dependencies(self) -> List[AbstractDependency]:
        """Return list of dependencies this task has on other tasks"""
        pass
    
    @abstractmethod
    def get_assigned_resource(self) -> Optional[AbstractResource]:
        """Return currently assigned resource (planning variable)"""
        pass
    
    @abstractmethod
    def set_assigned_resource(self, resource: Optional[AbstractResource]) -> None:
        """Set assigned resource (planning variable)"""
        pass
    
    @abstractmethod
    def get_start_time(self) -> Optional[datetime.datetime]:
        """Return scheduled start time (planning variable)"""
        pass
    
    @abstractmethod
    def set_start_time(self, start_time: Optional[datetime.datetime]) -> None:
        """Set scheduled start time (planning variable)"""
        pass
    
    def get_end_time(self) -> Optional[datetime.datetime]:
        """Return calculated end time based on start time and duration"""
        start = self.get_start_time()
        return start + self.get_duration() if start else None


class AbstractSchedule(ABC, Generic[ResourceType, TaskType]):
    """
    Abstract base class for scheduling solutions.
    
    Contains the complete problem definition including resources, tasks,
    and time ranges, plus the optimization score.
    """
    
    @abstractmethod
    def get_resources(self) -> List[ResourceType]:
        """Return list of available resources"""
        pass
    
    @abstractmethod
    def get_tasks(self) -> List[TaskType]:
        """Return list of tasks to be scheduled"""
        pass
    
    @abstractmethod
    def get_time_range(self) -> List[datetime.datetime]:
        """Return list of possible start times for tasks"""
        pass
    
    @abstractmethod
    def get_score(self) -> Optional[HardSoftScore]:
        """Return current optimization score"""
        pass
    
    @abstractmethod
    def set_score(self, score: Optional[HardSoftScore]) -> None:
        """Set optimization score"""
        pass
