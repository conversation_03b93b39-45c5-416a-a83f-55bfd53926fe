"""
Domain-Agnostic Healthcare Scheduling Framework
Based on TimeFold Solver architecture patterns adapted for Python
"""

import datetime
from typing import List, Set, Optional, Annotated, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

from timefold.solver.domain import planning_entity, planning_solution
from timefold.solver.domain import PlanningVariable, PlanningId, ValueRangeProvider
from timefold.solver.domain import ProblemFactCollectionProperty, PlanningEntityCollectionProperty, PlanningScore
from timefold.solver.score import HardSoftScore


# ============================================================================
# Core Domain Abstractions (Domain-Agnostic)
# ============================================================================

class SkillLevel(Enum):
    BASIC = 1
    INTERMEDIATE = 2
    ADVANCED = 3
    EXPERT = 4
    SPECIALIST = 5


class Priority(Enum):
    CRITICAL = 1000    # Life-threatening
    URGENT = 100       # Same-day required
    HIGH = 50          # Within 24 hours
    ROUTINE = 10       # Standard care
    WELLNESS = 1       # Preventive care


class LocationType(Enum):
    CLINIC = "clinic"
    PATIENT_HOME = "patient_home"
    HOSPITAL = "hospital"
    PHARMACY = "pharmacy"
    EMERGENCY = "emergency"


@dataclass(frozen=True)
class Skill:
    """Domain-agnostic skill representation with hierarchy support"""
    id: str
    name: str
    level: SkillLevel
    parent_skills: List[str] = field(default_factory=list)
    child_skills: List[str] = field(default_factory=list)
    certification_required: bool = False
    expiration_tracking: bool = False


@dataclass(frozen=True)
class Location:
    """Geographic location with healthcare-specific attributes"""
    id: str
    name: str
    latitude: float
    longitude: float
    location_type: LocationType
    address: str
    timezone: str = "UTC"
    safety_rating: int = 5  # 1-5 scale
    accessibility_features: List[str] = field(default_factory=list)
    parking_available: bool = True


@dataclass(frozen=True)
class TimeWindow:
    """Flexible time window representation"""
    start: datetime.datetime
    end: datetime.datetime
    timezone: str = "UTC"
    is_preferred: bool = False
    flexibility_minutes: int = 0  # How much the window can shift


# ============================================================================
# Resource Abstractions
# ============================================================================

@dataclass
class Resource(ABC):
    """Abstract base class for all schedulable resources"""
    id: Annotated[str, PlanningId]
    name: str
    skills: List[Skill]
    availability_windows: List[TimeWindow]
    location: Optional[Location] = None
    priority: int = 5  # 1-10 scale for preference weighting
    
    @abstractmethod
    def can_perform_skill(self, required_skill: Skill) -> bool:
        """Check if resource can perform the required skill"""
        pass
    
    @abstractmethod
    def is_available_at(self, time: datetime.datetime) -> bool:
        """Check if resource is available at given time"""
        pass


@dataclass
class HealthcareResource(Resource):
    """Healthcare-specific resource (clinician, equipment, etc.)"""
    license_number: Optional[str] = None
    certifications: List[str] = field(default_factory=list)
    specializations: List[str] = field(default_factory=list)
    max_daily_hours: int = 8
    max_weekly_hours: int = 40
    requires_supervision: bool = False
    can_supervise: bool = False
    
    def can_perform_skill(self, required_skill: Skill) -> bool:
        """Check skill compatibility with hierarchy support"""
        # Direct match
        if required_skill.id in [s.id for s in self.skills]:
            return True
        
        # Check if any of our skills can substitute (higher level)
        for our_skill in self.skills:
            if (our_skill.level.value > required_skill.level.value and 
                required_skill.id in our_skill.parent_skills):
                return True
        
        return False
    
    def is_available_at(self, time: datetime.datetime) -> bool:
        """Check availability within time windows"""
        for window in self.availability_windows:
            if window.start <= time <= window.end:
                return True
        return False


# ============================================================================
# Task Abstractions
# ============================================================================

@dataclass
class Task(ABC):
    """Abstract base class for all schedulable tasks"""
    id: Annotated[str, PlanningId]
    name: str
    duration_minutes: int
    required_skills: List[Skill]
    preferred_skills: List[Skill] = field(default_factory=list)
    time_windows: List[TimeWindow] = field(default_factory=list)
    location: Optional[Location] = None
    priority: Priority = Priority.ROUTINE
    
    @abstractmethod
    def get_end_time(self) -> Optional[datetime.datetime]:
        """Calculate end time based on start time and duration"""
        pass


@planning_entity
@dataclass
class HealthcareTask(Task):
    """Healthcare-specific task (patient visit, procedure, etc.)"""
    patient_id: str = ""
    service_type: str = ""
    clinical_notes: str = ""
    equipment_required: List[str] = field(default_factory=list)
    infection_control_level: str = "STANDARD"
    continuity_requirements: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    # Planning Variables (what TimeFold optimizes)
    assigned_resource: Annotated[
        Optional[HealthcareResource], 
        PlanningVariable(value_range_provider_refs=['resourceRange'])
    ] = field(default=None)
    
    start_time: Annotated[
        Optional[datetime.datetime], 
        PlanningVariable(value_range_provider_refs=['timeRange'])
    ] = field(default=None)
    
    def get_end_time(self) -> Optional[datetime.datetime]:
        """Calculate end time based on start time and duration"""
        if self.start_time:
            return self.start_time + datetime.timedelta(minutes=self.duration_minutes)
        return None
    
    def is_pinned(self) -> bool:
        """Check if this task should not be moved by the solver"""
        return hasattr(self, 'fixed_assignment') and self.fixed_assignment


# ============================================================================
# Constraint Configuration
# ============================================================================

@dataclass
class ConstraintWeight:
    """Configurable constraint weights for domain adaptation"""
    hard_weight: str = "1hard"
    soft_weight: str = "1soft"
    multiplier: int = 1
    enabled: bool = True


@dataclass
class ConstraintConfiguration:
    """Domain-agnostic constraint configuration"""
    skill_mismatch: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    resource_conflict: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    time_window_violation: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    capacity_exceeded: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("1hard"))
    
    # Soft constraints for optimization
    minimize_travel: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "1soft"))
    balance_workload: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "5soft"))
    prefer_continuity: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "10soft"))
    minimize_cost: ConstraintWeight = field(default_factory=lambda: ConstraintWeight("0hard", "1soft"))


# ============================================================================
# Planning Solution
# ============================================================================

@planning_solution
@dataclass
class HealthcareSchedule:
    """Domain-agnostic healthcare scheduling solution"""
    
    # Problem Facts (immutable input data)
    resources: Annotated[
        List[HealthcareResource], 
        ProblemFactCollectionProperty, 
        ValueRangeProvider(id='resourceRange')
    ]
    
    time_slots: Annotated[
        List[datetime.datetime], 
        ProblemFactCollectionProperty, 
        ValueRangeProvider(id='timeRange')
    ]
    
    locations: Annotated[
        List[Location], 
        ProblemFactCollectionProperty
    ]
    
    # Planning Entities (what gets optimized)
    tasks: Annotated[List[HealthcareTask], PlanningEntityCollectionProperty]
    
    # Configuration
    constraint_config: ConstraintConfiguration = field(default_factory=ConstraintConfiguration)
    
    # Solution Quality Score
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
    
    # Metadata
    schedule_date: datetime.date = field(default_factory=datetime.date.today)
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)
    
    def get_tasks_for_resource(self, resource: HealthcareResource) -> List[HealthcareTask]:
        """Get all tasks assigned to a specific resource"""
        return [task for task in self.tasks if task.assigned_resource == resource]
    
    def get_unassigned_tasks(self) -> List[HealthcareTask]:
        """Get all tasks that haven't been assigned to any resource"""
        return [task for task in self.tasks if task.assigned_resource is None]
    
    def calculate_total_travel_distance(self) -> float:
        """Calculate total travel distance for all resources"""
        # This would integrate with Google Maps API
        # Implementation in separate service class
        return 0.0
    
    def get_schedule_summary(self) -> Dict[str, Any]:
        """Generate summary statistics for the schedule"""
        return {
            "total_tasks": len(self.tasks),
            "assigned_tasks": len([t for t in self.tasks if t.assigned_resource]),
            "unassigned_tasks": len(self.get_unassigned_tasks()),
            "total_resources": len(self.resources),
            "active_resources": len(set(t.assigned_resource for t in self.tasks if t.assigned_resource)),
            "schedule_date": self.schedule_date.isoformat(),
            "score": str(self.score) if self.score else "Not calculated"
        }


# ============================================================================
# Domain-Specific Extensions (Examples)
# ============================================================================

class HealthcareSkills:
    """Predefined healthcare skills with hierarchy"""
    
    # Nursing Skills
    WOUND_CARE_BASIC = Skill("WOUND_CARE_BASIC", "Basic Wound Care", SkillLevel.BASIC)
    WOUND_CARE_ADVANCED = Skill("WOUND_CARE_ADVANCED", "Advanced Wound Care", SkillLevel.ADVANCED, 
                               parent_skills=["WOUND_CARE_BASIC"])
    WOUND_CARE_SPECIALIST = Skill("WOUND_CARE_SPECIALIST", "Wound Care Specialist", SkillLevel.SPECIALIST,
                                 parent_skills=["WOUND_CARE_BASIC", "WOUND_CARE_ADVANCED"])
    
    # Medication Administration
    MEDICATION_BASIC = Skill("MEDICATION_BASIC", "Basic Medication", SkillLevel.BASIC)
    MEDICATION_IV = Skill("MEDICATION_IV", "IV Medication", SkillLevel.ADVANCED,
                         parent_skills=["MEDICATION_BASIC"], certification_required=True)
    
    # Therapy Skills
    PHYSICAL_THERAPY = Skill("PHYSICAL_THERAPY", "Physical Therapy", SkillLevel.EXPERT,
                           certification_required=True)
    OCCUPATIONAL_THERAPY = Skill("OCCUPATIONAL_THERAPY", "Occupational Therapy", SkillLevel.EXPERT,
                               certification_required=True)


class HealthcareServiceTypes:
    """Common healthcare service types"""
    ASSESSMENT = "ASSESSMENT"
    WOUND_CARE = "WOUND_CARE"
    MEDICATION_ADMIN = "MEDICATION_ADMIN"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    OCCUPATIONAL_THERAPY = "OCCUPATIONAL_THERAPY"
    WELLNESS_CHECK = "WELLNESS_CHECK"
    EMERGENCY_RESPONSE = "EMERGENCY_RESPONSE"
