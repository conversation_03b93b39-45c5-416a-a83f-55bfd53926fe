"""
Core Utilities - Shared Helper Functions and Utilities
======================================================

This module contains utility functions and helper classes that are used
across the scheduling framework. These utilities are domain-agnostic and
can be reused by any domain-specific implementation.

Key Components:
- TimeUtils: Time and date manipulation utilities
- LocationUtils: Geographic and location-related utilities
- ScheduleUtils: Schedule analysis and manipulation utilities
- ValidationUtils: Data validation utilities
"""

import datetime
import math
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass

from .domain import Resource, Task, Location, TimeWindow, Priority


class TimeUtils:
    """Utilities for time and date manipulation"""
    
    @staticmethod
    def create_time_window(start_hour: int, start_minute: int, 
                          end_hour: int, end_minute: int,
                          date: datetime.date = None,
                          timezone: str = "UTC") -> TimeWindow:
        """Create a time window for a specific date"""
        if date is None:
            date = datetime.date.today()
        
        start = datetime.datetime.combine(date, datetime.time(start_hour, start_minute))
        end = datetime.datetime.combine(date, datetime.time(end_hour, end_minute))
        
        return TimeWindow(start=start, end=end, timezone=timezone)
    
    @staticmethod
    def create_daily_availability(start_hour: int, end_hour: int,
                                 date: datetime.date = None,
                                 timezone: str = "UTC") -> TimeWindow:
        """Create a daily availability window"""
        return TimeUtils.create_time_window(
            start_hour, 0, end_hour, 0, date, timezone
        )
    
    @staticmethod
    def get_business_hours(date: datetime.date = None,
                          timezone: str = "UTC") -> TimeWindow:
        """Get standard business hours (9 AM - 5 PM)"""
        return TimeUtils.create_time_window(9, 0, 17, 0, date, timezone)
    
    @staticmethod
    def is_weekend(date: datetime.date) -> bool:
        """Check if date falls on weekend"""
        return date.weekday() >= 5  # Saturday = 5, Sunday = 6
    
    @staticmethod
    def is_business_day(date: datetime.date) -> bool:
        """Check if date is a business day (Monday-Friday)"""
        return not TimeUtils.is_weekend(date)
    
    @staticmethod
    def add_business_days(start_date: datetime.date, days: int) -> datetime.date:
        """Add business days to a date (skipping weekends)"""
        current_date = start_date
        days_added = 0
        
        while days_added < days:
            current_date += datetime.timedelta(days=1)
            if TimeUtils.is_business_day(current_date):
                days_added += 1
        
        return current_date
    
    @staticmethod
    def time_overlap_minutes(window1: TimeWindow, window2: TimeWindow) -> int:
        """Calculate overlap between two time windows in minutes"""
        # Convert to same timezone if needed
        if window1.timezone != window2.timezone:
            window2 = window2.to_timezone(window1.timezone)
        
        overlap_start = max(window1.start, window2.start)
        overlap_end = min(window1.end, window2.end)
        
        if overlap_start < overlap_end:
            return int((overlap_end - overlap_start).total_seconds() / 60)
        
        return 0


class LocationUtils:
    """Utilities for geographic and location operations"""
    
    @staticmethod
    def calculate_distance_km(loc1: Location, loc2: Location) -> Optional[float]:
        """Calculate distance between two locations in kilometers"""
        if (loc1.latitude is None or loc1.longitude is None or
            loc2.latitude is None or loc2.longitude is None):
            return None
        
        return loc1.distance_to(loc2)
    
    @staticmethod
    def find_nearest_location(target: Location, 
                            candidates: List[Location]) -> Optional[Location]:
        """Find the nearest location from a list of candidates"""
        if not candidates:
            return None
        
        nearest = None
        min_distance = float('inf')
        
        for candidate in candidates:
            distance = LocationUtils.calculate_distance_km(target, candidate)
            if distance is not None and distance < min_distance:
                min_distance = distance
                nearest = candidate
        
        return nearest
    
    @staticmethod
    def locations_within_radius(center: Location, 
                               candidates: List[Location],
                               radius_km: float) -> List[Location]:
        """Find all locations within a specified radius"""
        within_radius = []
        
        for candidate in candidates:
            distance = LocationUtils.calculate_distance_km(center, candidate)
            if distance is not None and distance <= radius_km:
                within_radius.append(candidate)
        
        return within_radius
    
    @staticmethod
    def create_location_grid(center_lat: float, center_lon: float,
                           grid_size_km: float, grid_count: int) -> List[Tuple[float, float]]:
        """Create a grid of coordinates around a center point"""
        # Approximate conversion: 1 degree ≈ 111 km
        lat_offset = grid_size_km / 111.0
        lon_offset = grid_size_km / (111.0 * math.cos(math.radians(center_lat)))
        
        coordinates = []
        half_count = grid_count // 2
        
        for i in range(-half_count, half_count + 1):
            for j in range(-half_count, half_count + 1):
                lat = center_lat + (i * lat_offset)
                lon = center_lon + (j * lon_offset)
                coordinates.append((lat, lon))
        
        return coordinates


class ScheduleUtils:
    """Utilities for schedule analysis and manipulation"""
    
    @staticmethod
    def calculate_total_duration(tasks: List[Task]) -> int:
        """Calculate total duration of tasks in minutes"""
        return sum(task.get_total_duration_minutes() for task in tasks)
    
    @staticmethod
    def group_tasks_by_resource(tasks: List[Task]) -> Dict[Optional[Resource], List[Task]]:
        """Group tasks by assigned resource"""
        groups = {}
        for task in tasks:
            resource = task.assigned_resource
            if resource not in groups:
                groups[resource] = []
            groups[resource].append(task)
        return groups
    
    @staticmethod
    def group_tasks_by_priority(tasks: List[Task]) -> Dict[Priority, List[Task]]:
        """Group tasks by priority level"""
        groups = {}
        for task in tasks:
            priority = task.priority
            if priority not in groups:
                groups[priority] = []
            groups[priority].append(task)
        return groups
    
    @staticmethod
    def get_task_conflicts(tasks: List[Task]) -> List[Tuple[Task, Task]]:
        """Find tasks that have scheduling conflicts (same resource, overlapping time)"""
        conflicts = []
        
        for i, task1 in enumerate(tasks):
            for task2 in tasks[i+1:]:
                if ScheduleUtils.tasks_conflict(task1, task2):
                    conflicts.append((task1, task2))
        
        return conflicts
    
    @staticmethod
    def tasks_conflict(task1: Task, task2: Task) -> bool:
        """Check if two tasks conflict (same resource, overlapping time)"""
        if (task1.assigned_resource != task2.assigned_resource or
            task1.assigned_resource is None or
            task1.start_time is None or task2.start_time is None):
            return False
        
        task1_end = task1.get_end_time()
        task2_end = task2.get_end_time()
        
        if task1_end is None or task2_end is None:
            return False
        
        # Check for time overlap
        return not (task1_end <= task2.start_time or task2_end <= task1.start_time)
    
    @staticmethod
    def sort_tasks_by_start_time(tasks: List[Task]) -> List[Task]:
        """Sort tasks by start time (unscheduled tasks at the end)"""
        scheduled = [t for t in tasks if t.start_time is not None]
        unscheduled = [t for t in tasks if t.start_time is None]
        
        scheduled.sort(key=lambda t: t.start_time)
        return scheduled + unscheduled
    
    @staticmethod
    def calculate_resource_workload_hours(resource: Resource, tasks: List[Task]) -> float:
        """Calculate total workload hours for a resource"""
        resource_tasks = [t for t in tasks if t.assigned_resource == resource]
        total_minutes = sum(t.get_total_duration_minutes() for t in resource_tasks)
        return total_minutes / 60.0
    
    @staticmethod
    def find_available_time_slots(resource: Resource, 
                                 duration_minutes: int,
                                 date: datetime.date,
                                 existing_tasks: List[Task] = None) -> List[datetime.datetime]:
        """Find available time slots for a resource on a given date"""
        if existing_tasks is None:
            existing_tasks = []
        
        available_slots = []
        resource_tasks = [t for t in existing_tasks if t.assigned_resource == resource]
        
        # Get resource availability windows for the date
        for window in resource.availability_windows:
            # Check if window is for the specified date
            if window.start.date() == date:
                current_time = window.start
                
                while current_time + datetime.timedelta(minutes=duration_minutes) <= window.end:
                    # Check if this slot conflicts with existing tasks
                    slot_end = current_time + datetime.timedelta(minutes=duration_minutes)
                    
                    conflict = False
                    for task in resource_tasks:
                        if (task.start_time is not None and 
                            not (slot_end <= task.start_time or 
                                 task.get_end_time() <= current_time)):
                            conflict = True
                            break
                    
                    if not conflict:
                        available_slots.append(current_time)
                    
                    # Move to next 15-minute slot
                    current_time += datetime.timedelta(minutes=15)
        
        return available_slots


class ValidationUtils:
    """Utilities for data validation"""
    
    @staticmethod
    def validate_resource(resource: Resource) -> List[str]:
        """Validate resource data and return list of errors"""
        errors = []
        
        if not resource.id:
            errors.append("Resource ID is required")
        
        if not resource.name:
            errors.append("Resource name is required")
        
        if resource.max_daily_hours <= 0:
            errors.append("Max daily hours must be positive")
        
        if resource.max_weekly_hours <= 0:
            errors.append("Max weekly hours must be positive")
        
        if resource.max_daily_hours > 24:
            errors.append("Max daily hours cannot exceed 24")
        
        if resource.max_weekly_hours > 168:
            errors.append("Max weekly hours cannot exceed 168")
        
        return errors
    
    @staticmethod
    def validate_task(task: Task) -> List[str]:
        """Validate task data and return list of errors"""
        errors = []
        
        if not task.id:
            errors.append("Task ID is required")
        
        if not task.name:
            errors.append("Task name is required")
        
        if task.duration_minutes <= 0:
            errors.append("Task duration must be positive")
        
        if task.duration_minutes > 1440:  # 24 hours
            errors.append("Task duration cannot exceed 24 hours")
        
        # Validate time windows
        for i, window in enumerate(task.time_windows):
            if window.start >= window.end:
                errors.append(f"Time window {i+1}: start time must be before end time")
        
        return errors
    
    @staticmethod
    def validate_location(location: Location) -> List[str]:
        """Validate location data and return list of errors"""
        errors = []
        
        if not location.id:
            errors.append("Location ID is required")
        
        if not location.name:
            errors.append("Location name is required")
        
        if location.latitude is not None:
            if not (-90 <= location.latitude <= 90):
                errors.append("Latitude must be between -90 and 90")
        
        if location.longitude is not None:
            if not (-180 <= location.longitude <= 180):
                errors.append("Longitude must be between -180 and 180")
        
        if not (1 <= location.safety_rating <= 5):
            errors.append("Safety rating must be between 1 and 5")
        
        return errors
    
    @staticmethod
    def validate_schedule_request(resources: List[Resource], 
                                 tasks: List[Task],
                                 locations: List[Location]) -> List[str]:
        """Validate a complete scheduling request"""
        errors = []
        
        if not resources:
            errors.append("At least one resource is required")
        
        if not tasks:
            errors.append("At least one task is required")
        
        # Validate individual components
        for resource in resources:
            resource_errors = ValidationUtils.validate_resource(resource)
            errors.extend([f"Resource {resource.id}: {err}" for err in resource_errors])
        
        for task in tasks:
            task_errors = ValidationUtils.validate_task(task)
            errors.extend([f"Task {task.id}: {err}" for err in task_errors])
        
        for location in locations:
            location_errors = ValidationUtils.validate_location(location)
            errors.extend([f"Location {location.id}: {err}" for err in location_errors])
        
        # Check for duplicate IDs
        resource_ids = [r.id for r in resources]
        if len(resource_ids) != len(set(resource_ids)):
            errors.append("Duplicate resource IDs found")
        
        task_ids = [t.id for t in tasks]
        if len(task_ids) != len(set(task_ids)):
            errors.append("Duplicate task IDs found")
        
        location_ids = [l.id for l in locations]
        if len(location_ids) != len(set(location_ids)):
            errors.append("Duplicate location IDs found")
        
        return errors
