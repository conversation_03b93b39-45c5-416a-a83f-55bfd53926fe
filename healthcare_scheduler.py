"""
Domain-Agnostic Healthcare Scheduling Engine
Main orchestration class that integrates TimeFold Solver with healthcare domain logic
"""

import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field

from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver import SolverFactory
from timefold.solver.score import HardSoftScore

from healthcare_domain import (
    HealthcareSchedule, HealthcareTask, HealthcareResource, Location, 
    ConstraintConfiguration, Priority, SkillLevel
)
from healthcare_constraints import define_healthcare_constraints
from google_maps_service import GoogleMapsHealthcareService


@dataclass
class SchedulingRequest:
    """Request object for healthcare scheduling"""
    resources: List[HealthcareResource]
    tasks: List[HealthcareTask]
    locations: List[Location]
    schedule_date: datetime.date
    constraint_config: Optional[ConstraintConfiguration] = None
    optimization_time_seconds: int = 30
    include_travel_optimization: bool = True


@dataclass
class SchedulingResult:
    """Result object containing the optimized schedule and metadata"""
    schedule: HealthcareSchedule
    optimization_time_seconds: float
    score: HardSoftScore
    statistics: Dict[str, Any]
    travel_summary: Optional[Dict[str, Any]] = None
    recommendations: List[str] = field(default_factory=list)


class HealthcareSchedulingEngine:
    """
    Main scheduling engine that orchestrates TimeFold Solver for healthcare scenarios
    """
    
    def __init__(self, google_maps_api_key: str = None):
        self.google_maps_service = None
        if google_maps_api_key:
            try:
                self.google_maps_service = GoogleMapsHealthcareService(google_maps_api_key)
            except Exception as e:
                print(f"Warning: Google Maps service not available: {e}")
        
        self.solver_factory = None
        self._initialize_solver()
    
    def _initialize_solver(self):
        """Initialize TimeFold Solver with healthcare constraints"""
        solver_config = SolverConfig(
            solution_class=HealthcareSchedule,
            entity_class_list=[HealthcareTask],
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_healthcare_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=30),
                best_score_limit=HardSoftScore.ZERO,
                unimproved_spent_limit=Duration(seconds=10)
            )
        )
        
        self.solver_factory = SolverFactory.create(solver_config)
    
    def create_schedule(self, request: SchedulingRequest) -> SchedulingResult:
        """
        Create an optimized healthcare schedule from the request
        """
        start_time = datetime.datetime.now()
        
        # Build the planning problem
        schedule = self._build_planning_problem(request)
        
        # Update solver configuration if needed
        if request.optimization_time_seconds != 30:
            self._update_solver_termination(request.optimization_time_seconds)
        
        # Solve the problem
        solver = self.solver_factory.build_solver()
        solved_schedule = solver.solve(schedule)
        
        end_time = datetime.datetime.now()
        optimization_time = (end_time - start_time).total_seconds()
        
        # Generate travel summary if Google Maps is available
        travel_summary = None
        if self.google_maps_service and request.include_travel_optimization:
            travel_summary = self._generate_travel_summary(solved_schedule)
        
        # Generate statistics and recommendations
        statistics = self._generate_statistics(solved_schedule)
        recommendations = self._generate_recommendations(solved_schedule, request)
        
        return SchedulingResult(
            schedule=solved_schedule,
            optimization_time_seconds=optimization_time,
            score=solved_schedule.score,
            statistics=statistics,
            travel_summary=travel_summary,
            recommendations=recommendations
        )
    
    def update_schedule_real_time(
        self,
        current_schedule: HealthcareSchedule,
        changes: List[Dict[str, Any]]
    ) -> SchedulingResult:
        """
        Update an existing schedule with real-time changes
        """
        # Apply changes to the schedule
        updated_schedule = self._apply_real_time_changes(current_schedule, changes)
        
        # Re-optimize with shorter time limit for real-time response
        solver_config = SolverConfig(
            solution_class=HealthcareSchedule,
            entity_class_list=[HealthcareTask],
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_healthcare_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=5),  # Shorter for real-time
                unimproved_spent_limit=Duration(seconds=2)
            )
        )
        
        solver_factory = SolverFactory.create(solver_config)
        solver = solver_factory.build_solver()
        
        start_time = datetime.datetime.now()
        solved_schedule = solver.solve(updated_schedule)
        end_time = datetime.datetime.now()
        
        optimization_time = (end_time - start_time).total_seconds()
        statistics = self._generate_statistics(solved_schedule)
        
        return SchedulingResult(
            schedule=solved_schedule,
            optimization_time_seconds=optimization_time,
            score=solved_schedule.score,
            statistics=statistics,
            recommendations=[]
        )
    
    def get_schedule_recommendations(
        self,
        schedule: HealthcareSchedule,
        new_task: HealthcareTask
    ) -> List[Dict[str, Any]]:
        """
        Get recommendations for scheduling a new task
        """
        recommendations = []
        
        # Find suitable resources
        suitable_resources = [
            resource for resource in schedule.resources
            if all(resource.can_perform_skill(skill) for skill in new_task.required_skills)
        ]
        
        # Find suitable time slots
        for resource in suitable_resources[:3]:  # Top 3 candidates
            for time_slot in schedule.time_slots:
                # Check if this assignment would be feasible
                temp_task = HealthcareTask(
                    id=new_task.id,
                    name=new_task.name,
                    duration_minutes=new_task.duration_minutes,
                    required_skills=new_task.required_skills,
                    time_windows=new_task.time_windows,
                    location=new_task.location,
                    priority=new_task.priority,
                    assigned_resource=resource,
                    start_time=time_slot
                )
                
                # Calculate impact score
                impact_score = self._calculate_assignment_impact(schedule, temp_task)
                
                recommendations.append({
                    "resource_id": resource.id,
                    "resource_name": resource.name,
                    "start_time": time_slot.isoformat(),
                    "impact_score": impact_score,
                    "skill_match_quality": self._calculate_skill_match_quality(resource, new_task),
                    "travel_impact": self._calculate_travel_impact(schedule, temp_task)
                })
        
        # Sort by impact score (lower is better)
        recommendations.sort(key=lambda x: x["impact_score"])
        return recommendations[:5]  # Return top 5 recommendations
    
    def _build_planning_problem(self, request: SchedulingRequest) -> HealthcareSchedule:
        """Build the TimeFold planning problem from the request"""
        
        # Generate time slots (15-minute intervals for 12 hours)
        start_time = datetime.datetime.combine(request.schedule_date, datetime.time(8, 0))
        time_slots = [
            start_time + datetime.timedelta(minutes=15 * i)
            for i in range(48)  # 12 hours * 4 slots per hour
        ]
        
        # Apply Google Maps constraints if available
        if self.google_maps_service:
            travel_constraints = self.google_maps_service.calculate_healthcare_constraints(
                request.tasks, request.resources
            )
            # Update constraint configuration with travel data
            # This would modify the constraint weights based on real travel times
        
        return HealthcareSchedule(
            resources=request.resources,
            time_slots=time_slots,
            locations=request.locations,
            tasks=request.tasks,
            constraint_config=request.constraint_config or ConstraintConfiguration(),
            schedule_date=request.schedule_date
        )
    
    def _update_solver_termination(self, seconds: int):
        """Update solver termination configuration"""
        solver_config = SolverConfig(
            solution_class=HealthcareSchedule,
            entity_class_list=[HealthcareTask],
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_healthcare_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=seconds),
                best_score_limit=HardSoftScore.ZERO,
                unimproved_spent_limit=Duration(seconds=max(5, seconds // 3))
            )
        )
        
        self.solver_factory = SolverFactory.create(solver_config)
    
    def _generate_travel_summary(self, schedule: HealthcareSchedule) -> Dict[str, Any]:
        """Generate travel summary using Google Maps data"""
        if not self.google_maps_service:
            return {}
        
        total_distance = 0
        total_duration = 0
        resource_summaries = {}
        
        for resource in schedule.resources:
            resource_tasks = schedule.get_tasks_for_resource(resource)
            if len(resource_tasks) < 2:
                continue
            
            # Sort tasks by start time
            sorted_tasks = sorted(resource_tasks, key=lambda t: t.start_time or datetime.datetime.min)
            
            resource_distance = 0
            resource_duration = 0
            
            for i in range(len(sorted_tasks) - 1):
                current_task = sorted_tasks[i]
                next_task = sorted_tasks[i + 1]
                
                if current_task.location and next_task.location:
                    travel_info = self.google_maps_service.get_travel_info(
                        current_task.location, next_task.location
                    )
                    resource_distance += travel_info.distance_meters
                    resource_duration += travel_info.duration_seconds
            
            total_distance += resource_distance
            total_duration += resource_duration
            
            resource_summaries[resource.id] = {
                "distance_km": resource_distance / 1000,
                "duration_hours": resource_duration / 3600,
                "estimated_fuel_cost": (resource_distance / 1000) * 0.15
            }
        
        return {
            "total_distance_km": total_distance / 1000,
            "total_duration_hours": total_duration / 3600,
            "estimated_total_fuel_cost": (total_distance / 1000) * 0.15,
            "resource_summaries": resource_summaries
        }
    
    def _generate_statistics(self, schedule: HealthcareSchedule) -> Dict[str, Any]:
        """Generate comprehensive statistics for the schedule"""
        assigned_tasks = [task for task in schedule.tasks if task.assigned_resource]
        unassigned_tasks = schedule.get_unassigned_tasks()
        
        # Resource utilization
        resource_utilization = {}
        for resource in schedule.resources:
            resource_tasks = schedule.get_tasks_for_resource(resource)
            total_minutes = sum(task.duration_minutes for task in resource_tasks)
            utilization_percent = (total_minutes / (resource.max_daily_hours * 60)) * 100
            resource_utilization[resource.id] = min(100, utilization_percent)
        
        # Priority distribution
        priority_stats = {}
        for priority in Priority:
            count = len([task for task in assigned_tasks if task.priority == priority])
            priority_stats[priority.name] = count
        
        return {
            "total_tasks": len(schedule.tasks),
            "assigned_tasks": len(assigned_tasks),
            "unassigned_tasks": len(unassigned_tasks),
            "assignment_rate": len(assigned_tasks) / len(schedule.tasks) * 100 if schedule.tasks else 0,
            "resource_utilization": resource_utilization,
            "average_utilization": sum(resource_utilization.values()) / len(resource_utilization) if resource_utilization else 0,
            "priority_distribution": priority_stats,
            "score_breakdown": {
                "hard_score": schedule.score.hard_score if schedule.score else 0,
                "soft_score": schedule.score.soft_score if schedule.score else 0
            }
        }
    
    def _generate_recommendations(
        self, 
        schedule: HealthcareSchedule, 
        request: SchedulingRequest
    ) -> List[str]:
        """Generate actionable recommendations based on the schedule"""
        recommendations = []
        
        unassigned_tasks = schedule.get_unassigned_tasks()
        if unassigned_tasks:
            high_priority_unassigned = [
                task for task in unassigned_tasks 
                if task.priority in [Priority.CRITICAL, Priority.URGENT]
            ]
            if high_priority_unassigned:
                recommendations.append(
                    f"URGENT: {len(high_priority_unassigned)} high-priority tasks remain unassigned. "
                    "Consider adding more resources or extending time windows."
                )
        
        # Check resource utilization
        statistics = self._generate_statistics(schedule)
        avg_utilization = statistics["average_utilization"]
        
        if avg_utilization > 90:
            recommendations.append(
                "High resource utilization detected. Consider adding more staff or "
                "redistributing workload to prevent burnout."
            )
        elif avg_utilization < 60:
            recommendations.append(
                "Low resource utilization detected. Consider optimizing staff allocation "
                "or taking on additional patients."
            )
        
        # Check for hard constraint violations
        if schedule.score and schedule.score.hard_score < 0:
            recommendations.append(
                f"Schedule has {abs(schedule.score.hard_score)} hard constraint violations. "
                "Review skill requirements, time windows, and resource availability."
            )
        
        return recommendations
    
    def _apply_real_time_changes(
        self, 
        schedule: HealthcareSchedule, 
        changes: List[Dict[str, Any]]
    ) -> HealthcareSchedule:
        """Apply real-time changes to the schedule"""
        # This would implement various change types:
        # - Add emergency task
        # - Remove unavailable resource
        # - Extend task duration
        # - Update time windows
        
        # For now, return the original schedule
        # In a full implementation, this would modify the schedule based on change types
        return schedule
    
    def _calculate_assignment_impact(self, schedule: HealthcareSchedule, task: HealthcareTask) -> float:
        """Calculate the impact score of assigning a task to a resource"""
        # Simplified impact calculation
        # In practice, this would run a quick constraint evaluation
        return 1.0
    
    def _calculate_skill_match_quality(self, resource: HealthcareResource, task: HealthcareTask) -> float:
        """Calculate how well a resource's skills match the task requirements"""
        exact_matches = 0
        total_required = len(task.required_skills)
        
        for required_skill in task.required_skills:
            if any(s.id == required_skill.id for s in resource.skills):
                exact_matches += 1
        
        return exact_matches / total_required if total_required > 0 else 0.0
    
    def _calculate_travel_impact(self, schedule: HealthcareSchedule, task: HealthcareTask) -> float:
        """Calculate the travel impact of adding this task"""
        if not self.google_maps_service or not task.location:
            return 0.0
        
        # Simplified travel impact calculation
        # In practice, this would calculate actual travel times
        return 1.0
