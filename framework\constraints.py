"""
Domain-Agnostic Scheduling Framework - Generic Constraints

This module provides generic constraint templates that work with abstract domain types.
These constraints can be applied to any domain that implements the abstract interfaces.

The constraints are organized into categories:
1. Hard Constraints: Must be satisfied for a valid solution
2. Soft Constraints: Preferences that improve solution quality

Key Features:
- Type-safe generic constraints using abstract base classes
- Reusable constraint patterns across domains
- Easy to extend with domain-specific constraints
- Compatible with TimeFold's constraint provider system
"""

import datetime
from typing import Type, Callable, Any
from timefold.solver.score import constraint_provider, ConstraintCollectors, Joiners
from timefold.solver.score import HardSoftScore
from .domain import AbstractTask, AbstractResource, AbstractSchedule


class GenericConstraintProvider:
    """
    Generic constraint provider that creates domain-agnostic constraints.
    
    This class provides factory methods for creating common scheduling constraints
    that work with any domain implementing the abstract interfaces.
    """
    
    def __init__(self, task_class: Type[AbstractTask]):
        """
        Initialize with the concrete task class for the domain.
        
        Args:
            task_class: The concrete task class that extends AbstractTask
        """
        self.task_class = task_class
    
    def create_constraint_provider(self) -> Callable:
        """
        Create a TimeFold constraint provider function.
        
        Returns:
            Function that can be used with ScoreDirectorFactoryConfig
        """
        @constraint_provider
        def define_constraints(constraint_factory):
            return [
                self.capability_mismatch_constraint(constraint_factory),
                self.location_mismatch_constraint(constraint_factory),
                self.time_window_violation_constraint(constraint_factory),
                self.resource_unavailable_constraint(constraint_factory),
                self.resource_overlap_constraint(constraint_factory),
                self.resource_capacity_constraint(constraint_factory),
                self.dependency_order_constraint(constraint_factory),
                self.dependency_same_resource_constraint(constraint_factory),
                # Soft constraints for optimization
                self.minimize_unassigned_tasks_constraint(constraint_factory),
                self.balance_resource_utilization_constraint(constraint_factory)
            ]
        
        return define_constraints
    
    def capability_mismatch_constraint(self, cf):
        """
        Hard constraint: Task's required capability must match resource's capabilities.
        
        Equivalent to healthcare's skill_mismatch constraint.
        """
        def has_capability_mismatch(task):
            if task.get_assigned_resource() is None:
                return False
            required_cap = task.get_required_capability()
            resource_caps = task.get_assigned_resource().get_capabilities()
            return not any(cap.is_compatible_with(required_cap) for cap in resource_caps)
        
        return (cf.for_each(self.task_class)
                .filter(has_capability_mismatch)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Capability mismatch'))
    
    def location_mismatch_constraint(self, cf):
        """
        Hard constraint: Task's location must be served by assigned resource.
        
        Equivalent to healthcare's service_area_mismatch constraint.
        """
        def has_location_mismatch(task):
            if task.get_assigned_resource() is None:
                return False
            task_location = task.get_location()
            resource_locations = task.get_assigned_resource().get_locations()
            return task_location not in resource_locations
        
        return (cf.for_each(self.task_class)
                .filter(has_location_mismatch)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Location mismatch'))
    
    def time_window_violation_constraint(self, cf):
        """
        Hard constraint: Task must be scheduled within its time window.
        
        Equivalent to healthcare's patient_window_violation constraint.
        """
        def violates_time_window(task):
            start_time = task.get_start_time()
            if start_time is None:
                return True  # Unassigned tasks violate constraint
            
            end_time = task.get_end_time()
            return (start_time < task.get_time_window_start() or 
                    end_time > task.get_time_window_end())
        
        return (cf.for_each(self.task_class)
                .filter(violates_time_window)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Time window violation'))
    
    def resource_unavailable_constraint(self, cf):
        """
        Hard constraint: Task must be scheduled during resource's availability.
        
        Equivalent to healthcare's clinician_unavailable constraint.
        """
        def resource_unavailable(task):
            resource = task.get_assigned_resource()
            start_time = task.get_start_time()
            
            if resource is None or start_time is None:
                return False
            
            end_time = task.get_end_time()
            availability_slots = resource.get_availability()
            
            return not any(slot.contains_range(start_time, end_time) 
                          for slot in availability_slots)
        
        return (cf.for_each(self.task_class)
                .filter(resource_unavailable)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Resource unavailable'))
    
    def resource_overlap_constraint(self, cf):
        """
        Hard constraint: Resource cannot be assigned to overlapping tasks.
        
        Equivalent to healthcare's clinician_overlap constraint.
        """
        def tasks_overlap(task1, task2):
            if (task1.get_assigned_resource() != task2.get_assigned_resource() or
                task1.get_start_time() is None or task2.get_start_time() is None):
                return False
            
            # Check if time ranges overlap
            task1_end = task1.get_end_time()
            task2_end = task2.get_end_time()
            
            return not (task1_end <= task2.get_start_time() or 
                       task2_end <= task1.get_start_time())
        
        return (cf.for_each_unique_pair(self.task_class)
                .filter(tasks_overlap)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Resource overlap'))
    
    def resource_capacity_constraint(self, cf):
        """
        Hard constraint: Resource cannot exceed its capacity limit.
        
        Equivalent to healthcare's max_visits_per_clinician constraint.
        """
        return (cf.for_each(self.task_class)
                .group_by(lambda task: task.get_assigned_resource(),
                         ConstraintCollectors.count())
                .filter(lambda resource, count: 
                       resource is not None and count > resource.get_capacity_limit())
                .penalize(HardSoftScore.ONE_HARD,
                         lambda resource, count: count - resource.get_capacity_limit())
                .as_constraint('Resource capacity exceeded'))
    
    def dependency_order_constraint(self, cf):
        """
        Hard constraint: Dependent tasks must be scheduled in correct order.
        
        Equivalent to healthcare's dependency_order constraint.
        """
        def is_dependency(predecessor, successor):
            return any(dep.get_predecessor_id() == predecessor.get_id() 
                      for dep in successor.get_dependencies())
        
        def order_violated(predecessor, successor):
            pred_start = predecessor.get_start_time()
            succ_start = successor.get_start_time()
            
            if pred_start is None or succ_start is None:
                return False
            
            # Find the dependency to get minimum gap
            dependency = next((dep for dep in successor.get_dependencies() 
                             if dep.get_predecessor_id() == predecessor.get_id()), None)
            
            if dependency is None:
                return False
            
            required_gap = dependency.get_minimum_gap()
            actual_gap = succ_start - predecessor.get_end_time()
            
            return actual_gap < required_gap
        
        return (cf.for_each(self.task_class)
                .join(self.task_class, Joiners.filtering(is_dependency))
                .filter(order_violated)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Dependency order violation'))
    
    def dependency_same_resource_constraint(self, cf):
        """
        Hard constraint: Tasks requiring same resource must use same resource.
        
        Equivalent to healthcare's dependency_same_clinician constraint.
        """
        def requires_same_resource(predecessor, successor):
            return any(dep.get_predecessor_id() == predecessor.get_id() and 
                      dep.requires_same_resource()
                      for dep in successor.get_dependencies())
        
        def different_resources(predecessor, successor):
            pred_resource = predecessor.get_assigned_resource()
            succ_resource = successor.get_assigned_resource()
            return (pred_resource is not None and succ_resource is not None and 
                   pred_resource != succ_resource)
        
        return (cf.for_each(self.task_class)
                .join(self.task_class, Joiners.filtering(requires_same_resource))
                .filter(different_resources)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Same resource dependency violation'))
    
    def minimize_unassigned_tasks_constraint(self, cf):
        """
        Soft constraint: Prefer solutions with fewer unassigned tasks.
        """
        return (cf.for_each(self.task_class)
                .filter(lambda task: task.get_assigned_resource() is None)
                .penalize(HardSoftScore.ONE_SOFT)
                .as_constraint('Unassigned task'))
    
    def balance_resource_utilization_constraint(self, cf):
        """
        Soft constraint: Prefer balanced utilization across resources.
        """
        # This is a simplified version - could be enhanced with variance calculation
        return (cf.for_each(self.task_class)
                .group_by(lambda task: task.get_assigned_resource(),
                         ConstraintCollectors.count())
                .filter(lambda resource, count: resource is not None and count > 1)
                .reward(HardSoftScore.ONE_SOFT, lambda resource, count: -abs(count - 3))
                .as_constraint('Balanced utilization'))
