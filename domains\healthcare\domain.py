"""
Healthcare Domain Model - Medical Scheduling Entities
=====================================================

This module contains healthcare-specific implementations of the core scheduling
framework. It extends the abstract base classes with medical constraints,
clinical requirements, and healthcare-specific business logic.

Architecture Diagram:
┌─────────────────────────────────────────────────────────────────────┐
│                    HEALTHCARE DOMAIN MODEL                         │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │ HealthcareResource │  │ HealthcareTask  │    │ HealthcareSkills│  │
│  │  (extends Resource)│  │ (extends Task)  │    │  (Value Objects)│  │
│  │                 │    │                 │    │                 │  │
│  │ + license_number│    │ + patient_id    │    │ + WOUND_CARE    │  │
│  │ + certifications│    │ + service_type  │    │ + MEDICATION    │  │
│  │ + specializations│   │ + clinical_notes│    │ + THERAPY       │  │
│  │ + supervision   │    │ + equipment_req │    │ + ASSESSMENT    │  │
│  │                 │    │ + infection_ctrl│    │ + EMERGENCY     │  │
│  │ + can_perform() │    │ + get_end_time()│    │                 │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│           │                       │                       │         │
│           │                       │                       │         │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │InfectionControl │    │ ServiceTypes    │    │ Planning Vars   │  │
│  │     (Enum)      │    │    (Constants)  │    │  (TimeFold)     │  │
│  │                 │    │                 │    │                 │  │
│  │ + STANDARD      │    │ + ASSESSMENT    │    │ + assigned_res  │  │
│  │ + CONTACT       │    │ + WOUND_CARE    │    │ + start_time    │  │
│  │ + DROPLET       │    │ + MEDICATION    │    │                 │  │
│  │ + AIRBORNE      │    │ + THERAPY       │    │                 │  │
│  │ + ISOLATION     │    │ + EMERGENCY     │    │                 │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘

Inheritance Pattern:
    Resource (Abstract Core)
    └── HealthcareResource (Medical specialization)
        ├── Nurse (RN, LPN with medical skills)
        ├── Therapist (PT, OT with therapy skills)
        └── Physician (MD with advanced medical skills)

    Task (Abstract Core)
    └── HealthcareTask (Patient visit specialization)
        ├── Assessment (Health evaluation)
        ├── Treatment (Wound care, medication)
        ├── Therapy (Physical, occupational)
        └── Emergency (Urgent medical response)

Organization:
1. Healthcare Resource Implementation (extends core Resource)
2. Healthcare Task Implementation (extends core Task)
3. Healthcare-Specific Enums (InfectionControlLevel, etc.)
4. Healthcare Value Objects (HealthcareSkills, ServiceTypes)
5. Medical Business Logic and Validation
"""

import datetime
from typing import List, Optional, Annotated
from dataclasses import dataclass, field
from enum import Enum

from timefold.solver.domain import planning_entity, PlanningVariable

from core.domain import Resource, Task, Location, Skill, SkillLevel, Priority, LocationType


# ============================================================================
# 1. HEALTHCARE RESOURCE IMPLEMENTATION (extends core Resource)
# ============================================================================

@dataclass
class HealthcareResource(Resource):
    """
    Healthcare-specific resource representing clinicians, nurses, therapists
    Extends core Resource with medical licensing and certification requirements

    Medical Specializations:
    - Registered Nurse (RN): Advanced medical skills, can supervise
    - Licensed Practical Nurse (LPN): Basic medical skills, requires supervision
    - Physical Therapist (PT): Specialized therapy skills
    - Occupational Therapist (OT): Specialized therapy skills
    - Physician (MD): Advanced medical and emergency skills

    Healthcare-Specific Attributes:
        license_number: Professional license identifier
        certifications: List of medical certifications (IV, CPR, etc.)
        specializations: Areas of medical expertise
        requires_supervision: Whether this resource needs supervision
        can_supervise: Whether this resource can supervise others

    Real-Time Healthcare Features:
        Staff illness tracking and replacement
        Emergency availability status
        Regulatory compliance monitoring
    """
    license_number: Optional[str] = None
    certifications: List[str] = field(default_factory=list)
    specializations: List[str] = field(default_factory=list)
    requires_supervision: bool = False
    can_supervise: bool = False

    def can_perform_skill(self, required_skill: Skill) -> bool:
        """
        Check skill compatibility with healthcare-specific hierarchy support
        Implements skill substitution rules for medical competencies
        """
        # Direct skill match
        for our_skill in self.skills:
            if our_skill.id == required_skill.id:
                return True

            # Check if our skill can substitute for required skill
            if our_skill.can_substitute_for(required_skill):
                return True

        return False

    def get_supervision_requirements(self) -> List[str]:
        """Get list of supervision requirements for this resource"""
        requirements = []

        if self.requires_supervision:
            requirements.append("Requires licensed supervisor present")

        # Check for skills requiring supervision
        for skill in self.skills:
            if skill.level == SkillLevel.BASIC and "MEDICATION" in skill.id:
                requirements.append(f"Medication administration requires supervision")

        return requirements

    def can_work_in_location_type(self, location_type: LocationType) -> bool:
        """Check if resource can work in specific location type"""
        # Home healthcare specific logic
        if location_type == LocationType.CLIENT_SITE:  # Patient homes
            # All healthcare resources can work in patient homes
            return True
        elif location_type == LocationType.OFFICE:  # Clinic work
            # Clinic work may require additional certifications
            return "CLINIC_CERTIFIED" in self.certifications
        elif location_type == LocationType.MOBILE:  # Emergency response
            # Emergency response requires specific skills
            return any(skill.id == "EMERGENCY_RESPONSE" for skill in self.skills)

        return True  # Default allow


# ============================================================================
# 2. HEALTHCARE TASK IMPLEMENTATION (extends core Task)
# ============================================================================

@planning_entity
@dataclass
class HealthcareTask(Task):
    """
    Healthcare-specific task representing patient visits, procedures, assessments
    Extends core Task with clinical requirements and medical constraints

    Clinical Task Types:
    - Assessment: Health evaluation and monitoring
    - Wound Care: Basic to advanced wound management
    - Medication Administration: Oral, IV, injection medications
    - Physical Therapy: Mobility and strength rehabilitation
    - Occupational Therapy: Daily living skills rehabilitation
    - Emergency Response: Urgent medical situations

    Healthcare-Specific Attributes:
        patient_id: Unique patient identifier
        service_type: Type of medical service being provided
        clinical_notes: Medical notes and special instructions
        equipment_required: Medical equipment needed for the visit
        infection_control_level: Required infection control precautions
        continuity_requirements: Preferences for same clinician

    Planning Variables (TimeFold optimizes these):
        assigned_resource: Which healthcare resource will perform this task
        start_time: When the medical visit will begin

    Real-Time Healthcare Features:
        Visit extensions for complex medical needs
        Emergency priority handling
        No-show and cancellation management
        Infection control compliance
    """
    patient_id: str = ""
    service_type: str = ""  # Will use HealthcareServiceTypes constants
    clinical_notes: str = ""
    equipment_required: List[str] = field(default_factory=list)
    infection_control_level: Optional['InfectionControlLevel'] = None  # Set after enum definition
    continuity_requirements: List[str] = field(default_factory=list)

    # Planning Variables (what TimeFold optimizes)
    assigned_resource: Annotated[
        Optional[HealthcareResource],
        PlanningVariable(value_range_provider_refs=['resourceRange'])
    ] = field(default=None)

    start_time: Annotated[
        Optional[datetime.datetime],
        PlanningVariable(value_range_provider_refs=['timeRange'])
    ] = field(default=None)

    def get_end_time(self) -> Optional[datetime.datetime]:
        """Calculate end time based on start time and total duration (including extensions)"""
        if self.start_time:
            total_duration = self.get_total_duration_minutes()
            return self.start_time + datetime.timedelta(minutes=total_duration)
        return None

    def requires_infection_control(self) -> bool:
        """Check if task requires special infection control measures"""
        return (self.infection_control_level is not None and
                self.infection_control_level != InfectionControlLevel.STANDARD)

    def get_required_equipment(self) -> List[str]:
        """Get list of required equipment based on service type and clinical notes"""
        equipment = self.equipment_required.copy()

        # Add service-type specific equipment
        if self.service_type == HealthcareServiceTypes.WOUND_CARE:
            equipment.extend(["wound_care_kit", "sterile_supplies"])
        elif self.service_type == HealthcareServiceTypes.MEDICATION_ADMIN:
            equipment.extend(["medication_supplies"])
        elif self.service_type == HealthcareServiceTypes.PHYSICAL_THERAPY:
            equipment.extend(["therapy_equipment"])

        # Add infection control equipment
        if self.requires_infection_control():
            equipment.extend(["ppe_kit", "disinfection_supplies"])

        return list(set(equipment))  # Remove duplicates

    def get_clinical_complexity_score(self) -> int:
        """Calculate complexity score based on clinical requirements (1-10)"""
        score = 1

        # Base complexity by service type
        complexity_map = {
            HealthcareServiceTypes.WELLNESS_CHECK: 1,
            HealthcareServiceTypes.ASSESSMENT: 3,
            HealthcareServiceTypes.MEDICATION_ADMIN: 4,
            HealthcareServiceTypes.WOUND_CARE: 5,
            HealthcareServiceTypes.PHYSICAL_THERAPY: 6,
            HealthcareServiceTypes.EMERGENCY_RESPONSE: 10
        }

        score = complexity_map.get(self.service_type, 3)

        # Adjust for infection control requirements
        if (self.infection_control_level and
            self.infection_control_level == InfectionControlLevel.ISOLATION):
            score += 3
        elif self.requires_infection_control():
            score += 1

        # Adjust for equipment requirements
        if len(self.equipment_required) > 3:
            score += 1

        # Adjust for clinical notes complexity (simple heuristic)
        if self.clinical_notes and len(self.clinical_notes) > 100:
            score += 1

        return min(10, score)  # Cap at 10

    def requires_continuity_of_care(self) -> bool:
        """Check if task requires continuity of care (same clinician)"""
        return bool(self.continuity_requirements) or self.service_type in [
            HealthcareServiceTypes.WOUND_CARE,
            HealthcareServiceTypes.PHYSICAL_THERAPY,
            HealthcareServiceTypes.OCCUPATIONAL_THERAPY
        ]

    def is_emergency(self) -> bool:
        """Check if this is an emergency task requiring immediate attention"""
        return (self.priority == Priority.CRITICAL or
                self.service_type == HealthcareServiceTypes.EMERGENCY_RESPONSE)

    def can_be_performed_by(self, resource: HealthcareResource) -> bool:
        """Check if this task can be performed by the given resource"""
        # Check basic skill requirements
        for required_skill in self.required_skills:
            if not resource.can_perform_skill(required_skill):
                return False

        # Check location type compatibility
        if self.location and not resource.can_work_in_location_type(self.location.location_type):
            return False

        # Check supervision requirements
        if resource.requires_supervision and not self._has_supervisor_available():
            return False

        # Check infection control capabilities
        if (self.infection_control_level == InfectionControlLevel.ISOLATION and
            "ISOLATION_CERTIFIED" not in resource.certifications):
            return False

        return True

    def _has_supervisor_available(self) -> bool:
        """Check if supervisor is available (simplified implementation)"""
        # In real implementation, this would check for available supervisors
        # in the same time window and location
        return True  # Simplified for now


# ============================================================================
# 3. HEALTHCARE-SPECIFIC ENUMS
# ============================================================================

class InfectionControlLevel(Enum):
    """Infection control levels for healthcare tasks"""
    STANDARD = "STANDARD"
    CONTACT_PRECAUTIONS = "CONTACT_PRECAUTIONS"
    DROPLET_PRECAUTIONS = "DROPLET_PRECAUTIONS"
    AIRBORNE_PRECAUTIONS = "AIRBORNE_PRECAUTIONS"
    ISOLATION = "ISOLATION"


# ============================================================================
# 4. HEALTHCARE VALUE OBJECTS (Skills and Service Types)
# ============================================================================

class HealthcareServiceTypes:
    """Common healthcare service types for home healthcare"""
    ASSESSMENT = "ASSESSMENT"
    WOUND_CARE = "WOUND_CARE"
    MEDICATION_ADMIN = "MEDICATION_ADMIN"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    OCCUPATIONAL_THERAPY = "OCCUPATIONAL_THERAPY"
    WELLNESS_CHECK = "WELLNESS_CHECK"
    EMERGENCY_RESPONSE = "EMERGENCY_RESPONSE"
    SKILLED_NURSING = "SKILLED_NURSING"
    PERSONAL_CARE = "PERSONAL_CARE"


class HealthcareSkills:
    """Predefined healthcare skills with hierarchy for home healthcare"""

    # Nursing Skills
    WOUND_CARE_BASIC = Skill(
        id="WOUND_CARE_BASIC",
        name="Basic Wound Care",
        level=SkillLevel.BASIC
    )

    WOUND_CARE_ADVANCED = Skill(
        id="WOUND_CARE_ADVANCED",
        name="Advanced Wound Care",
        level=SkillLevel.ADVANCED,
        parent_skills=["WOUND_CARE_BASIC"]
    )

    WOUND_CARE_SPECIALIST = Skill(
        id="WOUND_CARE_SPECIALIST",
        name="Wound Care Specialist",
        level=SkillLevel.EXPERT,
        parent_skills=["WOUND_CARE_BASIC", "WOUND_CARE_ADVANCED"]
    )

    # Medication Administration
    MEDICATION_BASIC = Skill(
        id="MEDICATION_BASIC",
        name="Basic Medication Administration",
        level=SkillLevel.BASIC
    )

    MEDICATION_IV = Skill(
        id="MEDICATION_IV",
        name="IV Medication Administration",
        level=SkillLevel.ADVANCED,
        parent_skills=["MEDICATION_BASIC"]
    )

    # Therapy Skills
    PHYSICAL_THERAPY = Skill(
        id="PHYSICAL_THERAPY",
        name="Physical Therapy",
        level=SkillLevel.EXPERT
    )

    OCCUPATIONAL_THERAPY = Skill(
        id="OCCUPATIONAL_THERAPY",
        name="Occupational Therapy",
        level=SkillLevel.EXPERT
    )

    # Assessment Skills
    HEALTH_ASSESSMENT = Skill(
        id="HEALTH_ASSESSMENT",
        name="Health Assessment",
        level=SkillLevel.INTERMEDIATE
    )

    # Emergency Response
    EMERGENCY_RESPONSE = Skill(
        id="EMERGENCY_RESPONSE",
        name="Emergency Response",
        level=SkillLevel.ADVANCED
    )


