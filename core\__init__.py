"""
Core Scheduling Framework - Domain-Agnostic Components
=====================================================

This package contains the reusable 80% of the scheduling framework that can be
adapted to any domain (healthcare, education, field service, etc.).

Core Components:
- domain: Abstract base classes for resources, tasks, locations
- constraints: Common constraint patterns and utilities
- solver: TimeFold solver integration and optimization engine
- utils: Shared utilities and helper functions

Usage:
    from core.domain import Resource, Task, Location
    from core.solver import SchedulingEngine
    from core.constraints import ConstraintProvider
"""

__version__ = "1.0.0"
__author__ = "Healthcare Scheduling Framework Team"

# Core exports for easy importing
from .domain import Resource, Task, Location, TimeWindow, Skill, SkillLevel
from .solver import SchedulingEngine, SchedulingRequest, SchedulingResult
from .constraints import ConstraintProvider

__all__ = [
    'Resource',
    'Task', 
    'Location',
    'TimeWindow',
    'Skill',
    'SkillLevel',
    'SchedulingEngine',
    'SchedulingRequest',
    'SchedulingResult',
    'ConstraintProvider'
]
