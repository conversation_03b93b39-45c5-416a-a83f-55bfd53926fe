# Domain-Agnostic Healthcare Scheduling Framework - TimeFold Python Implementation

A comprehensive healthcare scheduling optimization system built with TimeFold Solver in Python, integrated with Google Maps API for real-world routing. This framework demonstrates how to create a domain-agnostic scheduling system that can adapt to various healthcare scenarios while leveraging TimeFold's advanced constraint solving capabilities.

## Key Features

- **Domain-Agnostic Design**: Adaptable to various healthcare scheduling scenarios
- **Google Maps Integration**: Real-world travel times and route optimization
- **Hierarchical Skill Matching**: Intelligent skill substitution and fallback
- **Real-Time Planning**: Handle emergencies, staff changes, and schedule disruptions
- **Advanced Constraints**: Safety pairing, regulatory compliance, continuity of care
- **Multi-Location Support**: Time zone aware scheduling across facilities

## Quick Start

### Prerequisites
```bash
pip install timefold==1.22.1b0
pip install googlemaps
pip install requests
```

### Environment Setup
```bash
export GOOGLE_API_KEY=AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4
```

### Run the Example
```bash
python main.py
```

### Expected Output
```
Score: 0hard/0soft
2025-06-01 09:00:00 Patient A -> Nurse Alice (Skill: WOUND_CARE_ADVANCED)
2025-06-01 10:00:00 Patient B -> Nurse Alice (Skill: MEDICATION_IV)
2025-06-01 10:45:00 Patient C -> Nurse Bob (Skill: PHYSICAL_THERAPY)
Travel Distance: 15.2 km, Estimated Cost: $12.50
```

## Project Structure

```
clinician-scheduler-python/
├── domain.py          # Domain model (entities, value objects)
├── constraints.py     # Business rules and scoring
├── main.py           # Application entry point
├── requirements.txt  # Dependencies
└── docs/             # Comprehensive documentation
    ├── README.md                 # Main guide with concepts and walkthrough
    ├── TECHNICAL_GUIDE.md        # Deep technical implementation details
    ├── EXAMPLES_GUIDE.md         # Practical real-world scenarios
    ├── TROUBLESHOOTING_FAQ.md    # Common issues and solutions
    └── QUICK_REFERENCE.md        # Handy reference for daily use
```

## What This System Solves

- **Problem**: Schedule patient visits to available clinicians
- **Goal**: Find optimal assignments that satisfy all business rules
- **Constraints**: Skill matching, availability, time windows, dependencies
- **Optimization**: Minimize conflicts and maximize efficiency

## Documentation Guide

### For Java Developers New to Python/TimeFold
Start here to understand the concepts and implementation:

1. **[Main Guide](docs/README.md)** - Comprehensive overview with Java-to-Python concept mapping
2. **[Technical Guide](docs/TECHNICAL_GUIDE.md)** - Deep dive into TimeFold implementation details
3. **[Quick Reference](docs/QUICK_REFERENCE.md)** - Handy reference for daily development

### For Practical Implementation
Use these guides to implement real-world features:

4. **[Examples Guide](docs/EXAMPLES_GUIDE.md)** - Common healthcare scheduling scenarios
5. **[Troubleshooting FAQ](docs/TROUBLESHOOTING_FAQ.md)** - Solutions to common issues

## Key Features Demonstrated

- **Multi-constraint optimization** with hard and soft constraints
- **Skill-based assignment** ensuring clinicians match visit requirements
- **Time window management** respecting patient availability
- **Dependency handling** for sequential visit requirements
- **Resource conflict resolution** preventing double-booking
- **Workload balancing** across multiple clinicians

## Example Scenario

The current implementation schedules 3 patient visits:
- **Visit A**: Patient A, 60 minutes, 9 AM - 12 PM window
- **Visit B**: Patient B, 45 minutes, 10 AM - 3 PM window, must be after Visit A
- **Visit C**: Patient C, 30 minutes, 11 AM - 4 PM window, must be after Visit B with same clinician

With 2 available clinicians (Alice and Bob), the solver finds the optimal schedule that satisfies all constraints.

## Business Rules Implemented

1. **Skill Matching**: Clinicians can only perform visits matching their skills
2. **Service Area Coverage**: Clinicians must serve the visit's geographic area
3. **Time Windows**: Visits must occur within patient-specified time ranges
4. **Availability**: Clinicians can only work during their available hours
5. **No Overlaps**: Clinicians cannot be double-booked
6. **Capacity Limits**: Clinicians have maximum daily visit limits
7. **Dependencies**: Some visits must occur in specific order
8. **Continuity**: Related visits can require the same clinician

## Extending the System

The modular design makes it easy to add new features:

- **Geographic optimization** with travel time constraints
- **Equipment management** for specialized medical devices
- **Priority scheduling** for urgent vs routine visits
- **Multi-day scheduling** across weeks or months
- **Real-time updates** for schedule changes
- **Advanced workload balancing** algorithms

## Technology Stack

- **Python 3.8+** - Programming language
- **TimeFold Solver** - Constraint satisfaction optimization engine
- **Dataclasses** - Clean domain model definition
- **Type Hints** - Better code documentation and IDE support

## Learning Path

1. **Run the example** to see the system in action
2. **Read the main guide** to understand TimeFold concepts
3. **Explore the constraints** to see how business rules are implemented
4. **Try the examples** to implement new features
5. **Use the quick reference** for ongoing development

## Support

- **Documentation**: Comprehensive guides in the `docs/` folder
- **Code Examples**: Real-world scenarios and patterns
- **Troubleshooting**: Common issues and solutions
- **Quick Reference**: Essential patterns and templates

This project serves as both a working healthcare scheduler and a comprehensive learning resource for Java developers transitioning to Python and TimeFold optimization.
