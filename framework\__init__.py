# Domain-Agnostic Scheduling Framework
# 
# This framework provides abstract base classes and interfaces for building
# scheduling optimization solutions using TimeFold. It separates core scheduling
# logic from domain-specific details using a provider-consumer model.

__version__ = "1.0.0"
__author__ = "Augment Agent"

from .domain import (
    AbstractResource,
    AbstractTask, 
    AbstractCapability,
    AbstractLocation,
    AbstractTimeSlot,
    AbstractDependency,
    AbstractSchedule
)

from .factory import SchedulingFrameworkFactory
from .constraints import GenericConstraintProvider

__all__ = [
    'AbstractResource',
    'AbstractTask',
    'AbstractCapability', 
    'AbstractLocation',
    'AbstractTimeSlot',
    'AbstractDependency',
    'AbstractSchedule',
    'SchedulingFrameworkFactory',
    'GenericConstraintProvider'
]
