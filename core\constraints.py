"""
Core Constraint Framework - Reusable Constraint Patterns
========================================================

This module provides the base constraint patterns and utilities that can be
reused across different domains. Domain-specific implementations can inherit
from these base patterns and add their own specialized constraints.

Key Components:
- ConstraintProvider: Abstract base class for constraint providers
- Common constraint patterns (skill matching, time windows, capacity, etc.)
- Constraint utilities and helper functions
"""

import datetime
from typing import List, Optional, Callable, Any
from abc import ABC, abstractmethod

from timefold.solver.score import constraint_provider, ConstraintCollectors, Joiners
from timefold.solver.score import HardSoftScore

from .domain import Resource, Task, Location, TimeWindow, Skill, Priority


class ConstraintProvider(ABC):
    """
    Abstract base class for constraint providers
    Domain-specific implementations should inherit from this class
    """
    
    @abstractmethod
    def define_constraints(self, constraint_factory):
        """Define all constraints for the domain"""
        pass
    
    def get_hard_constraints(self, constraint_factory) -> List:
        """Get hard constraints (must be satisfied)"""
        return [
            self.skill_requirement_violation(constraint_factory),
            self.resource_capacity_exceeded(constraint_factory),
            self.time_window_violation(constraint_factory),
            self.resource_double_booking(constraint_factory),
        ]
    
    def get_soft_constraints(self, constraint_factory) -> List:
        """Get soft constraints (optimization goals)"""
        return [
            self.balance_workload_distribution(constraint_factory),
            self.prefer_skill_exact_match(constraint_factory),
        ]
    
    # ========================================================================
    # Core Hard Constraints (Reusable Patterns)
    # ========================================================================
    
    def skill_requirement_violation(self, cf):
        """
        Hard constraint: Resource must have required skills for the task
        Supports hierarchical skill matching
        """
        def lacks_required_skill(task):
            if task.assigned_resource is None:
                return False
            
            for required_skill in task.required_skills:
                if not task.assigned_resource.can_perform_skill(required_skill):
                    return True
            return False
        
        return (cf.for_each(Task)
                .filter(lacks_required_skill)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Skill requirement violation'))
    
    def resource_capacity_exceeded(self, cf):
        """
        Hard constraint: Resource cannot be assigned more tasks than capacity allows
        """
        def exceeds_daily_capacity(resource, task_count):
            if resource is None:
                return False
            # Calculate total hours from task count (simplified)
            total_hours = task_count  # Would calculate actual duration in real implementation
            return total_hours > resource.max_daily_hours
        
        return (cf.for_each(Task)
                .filter(lambda task: task.assigned_resource is not None)
                .group_by(lambda task: task.assigned_resource, ConstraintCollectors.count())
                .filter(exceeds_daily_capacity)
                .penalize(HardSoftScore.ONE_HARD, 
                         lambda resource, count: count - resource.max_daily_hours)
                .as_constraint('Resource capacity exceeded'))
    
    def time_window_violation(self, cf):
        """
        Hard constraint: Task must be scheduled within its allowed time windows
        """
        def violates_time_window(task):
            if task.start_time is None or not task.time_windows:
                return False
            
            task_end = task.get_end_time()
            for window in task.time_windows:
                if window.start <= task.start_time and task_end <= window.end:
                    return False
            return True
        
        return (cf.for_each(Task)
                .filter(violates_time_window)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Time window violation'))
    
    def resource_double_booking(self, cf):
        """
        Hard constraint: Resource cannot be assigned to overlapping tasks
        """
        def tasks_overlap(task1, task2):
            if (task1.assigned_resource != task2.assigned_resource or 
                task1.assigned_resource is None or
                task1.start_time is None or task2.start_time is None):
                return False
            
            task1_end = task1.get_end_time()
            task2_end = task2.get_end_time()
            
            # Check for overlap
            return not (task1_end <= task2.start_time or task2_end <= task1.start_time)
        
        return (cf.for_each_unique_pair(Task)
                .filter(tasks_overlap)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Resource double booking'))
    
    def dependency_order_violation(self, cf):
        """
        Hard constraint: Dependent tasks must be scheduled in correct order
        """
        def violates_dependency_order(task1, task2):
            if (task1.start_time is None or task2.start_time is None or
                task2.id not in task1.dependencies):
                return False
            
            # task2 depends on task1, so task1 must finish before task2 starts
            task1_end = task1.get_end_time()
            return task2.start_time < task1_end
        
        return (cf.for_each(Task)
                .join(Task, 
                      Joiners.filtering(lambda t1, t2: t2.id in t1.dependencies))
                .filter(violates_dependency_order)
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint('Dependency order violation'))
    
    # ========================================================================
    # Core Soft Constraints (Optimization Patterns)
    # ========================================================================
    
    def balance_workload_distribution(self, cf):
        """
        Soft constraint: Balance workload across resources
        """
        def calculate_workload_imbalance(resource, task_count):
            if resource is None:
                return 0
            
            # Penalize deviation from ideal workload
            ideal_workload = 6  # tasks per day (configurable)
            deviation = abs(task_count - ideal_workload)
            return deviation * deviation  # Quadratic penalty for fairness
        
        return (cf.for_each(Task)
                .filter(lambda task: task.assigned_resource is not None)
                .group_by(lambda task: task.assigned_resource, ConstraintCollectors.count())
                .penalize(HardSoftScore.ONE_SOFT, calculate_workload_imbalance)
                .as_constraint('Balance workload distribution'))
    
    def prefer_skill_exact_match(self, cf):
        """
        Soft constraint: Prefer exact skill matches over hierarchical substitutions
        """
        def uses_skill_substitution(task):
            if task.assigned_resource is None:
                return False
            
            # Check if using higher-level skill for lower-level task
            for required_skill in task.required_skills:
                exact_match = any(s.id == required_skill.id for s in task.assigned_resource.skills)
                if not exact_match:
                    # Using skill substitution
                    return True
            return False
        
        return (cf.for_each(Task)
                .filter(uses_skill_substitution)
                .penalize(HardSoftScore.ONE_SOFT)
                .as_constraint('Prefer skill exact match'))
    
    def prioritize_high_priority_tasks(self, cf):
        """
        Soft constraint: Prioritize high-priority tasks for assignment
        """
        def is_unassigned_high_priority(task):
            return (task.assigned_resource is None and 
                    task.priority in [Priority.URGENT, Priority.CRITICAL])
        
        return (cf.for_each(Task)
                .filter(is_unassigned_high_priority)
                .penalize(HardSoftScore.of(0, 10))  # Fixed penalty value
                .as_constraint('Prioritize high priority tasks'))
    
    def prefer_resource_preferences(self, cf):
        """
        Soft constraint: Consider resource preferences and priorities
        """
        def violates_preferences(task):
            if task.assigned_resource is None:
                return False
            
            # Use resource priority as preference indicator
            return task.assigned_resource.priority > 7  # Lower preference resources
        
        return (cf.for_each(Task)
                .filter(violates_preferences)
                .penalize(HardSoftScore.ONE_SOFT)
                .as_constraint('Prefer resource preferences'))


# ============================================================================
# Constraint Utilities
# ============================================================================

class ConstraintUtils:
    """Utility functions for constraint calculations"""
    
    @staticmethod
    def calculate_time_overlap_minutes(task1: Task, task2: Task) -> int:
        """Calculate overlap time between two tasks in minutes"""
        if (task1.start_time is None or task2.start_time is None):
            return 0
        
        task1_end = task1.get_end_time()
        task2_end = task2.get_end_time()
        
        if task1_end is None or task2_end is None:
            return 0
        
        # Calculate overlap
        overlap_start = max(task1.start_time, task2.start_time)
        overlap_end = min(task1_end, task2_end)
        
        if overlap_start < overlap_end:
            return int((overlap_end - overlap_start).total_seconds() / 60)
        
        return 0
    
    @staticmethod
    def is_within_time_window(task: Task, window: TimeWindow) -> bool:
        """Check if task is scheduled within the given time window"""
        if task.start_time is None:
            return False
        
        task_end = task.get_end_time()
        if task_end is None:
            return False
        
        return window.start <= task.start_time and task_end <= window.end
    
    @staticmethod
    def calculate_skill_match_score(resource: Resource, task: Task) -> float:
        """Calculate how well a resource's skills match task requirements (0-1)"""
        if not task.required_skills:
            return 1.0
        
        total_skills = len(task.required_skills)
        matched_skills = 0
        exact_matches = 0
        
        for required_skill in task.required_skills:
            if resource.can_perform_skill(required_skill):
                matched_skills += 1
                # Check for exact match
                if any(s.id == required_skill.id for s in resource.skills):
                    exact_matches += 1
        
        # Base match score
        match_score = matched_skills / total_skills
        
        # Bonus for exact matches
        exact_bonus = (exact_matches / total_skills) * 0.2
        
        return min(1.0, match_score + exact_bonus)
    
    @staticmethod
    def calculate_priority_weight(priority: Priority) -> int:
        """Convert priority enum to numeric weight for constraints"""
        priority_weights = {
            Priority.LOW: 1,
            Priority.ROUTINE: 2,
            Priority.HIGH: 5,
            Priority.URGENT: 10,
            Priority.CRITICAL: 20
        }
        return priority_weights.get(priority, 2)
