"""
Core Scheduling Engine - Domain-Agnostic TimeFold Integration
=============================================================

This module provides the core scheduling engine that integrates with TimeFold Solver.
It's designed to be domain-agnostic and can be extended by domain-specific implementations.

Key Components:
- SchedulingEngine: Main solver integration and optimization logic
- SchedulingRequest: Input data structure for scheduling requests
- SchedulingResult: Output data structure with optimization results
- Schedule: Container for the optimized schedule
"""

import datetime
from typing import List, Optional, Dict, Any, Type
from dataclasses import dataclass, field
from abc import ABC, abstractmethod

from timefold.solver import SolverConfig, SolverFactory
from timefold.solver.domain import planning_solution, planning_entity
from timefold.solver.domain import PlanningEntityCollectionProperty, ProblemFactCollectionProperty
from timefold.solver.domain import PlanningScore, ValueRangeProvider, PlanningVariable
from timefold.solver.score import HardSoftScore
from typing import Annotated

from .domain import Resource, Task, Location, TimeWindow
from .constraints import ConstraintProvider


@dataclass
class SchedulingRequest:
    """
    Input data structure for scheduling requests
    Contains all the data needed to create an optimized schedule
    """
    resources: List[Resource]
    tasks: List[Task]
    locations: List[Location] = field(default_factory=list)
    schedule_date: datetime.date = field(default_factory=datetime.date.today)
    optimization_time_seconds: int = 30
    include_travel_optimization: bool = False
    constraint_weights: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SchedulingResult:
    """
    Output data structure containing optimization results
    """
    schedule: 'Schedule'
    score: HardSoftScore
    optimization_time_seconds: float
    statistics: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    travel_summary: Optional[Dict[str, Any]] = None
    unassigned_tasks: List[Task] = field(default_factory=list)
    
    def is_feasible(self) -> bool:
        """Check if the solution is feasible (no hard constraint violations)"""
        return self.score.hard_score >= 0
    
    def get_assignment_rate(self) -> float:
        """Calculate percentage of tasks that were assigned"""
        total_tasks = len(self.schedule.tasks)
        if total_tasks == 0:
            return 100.0
        
        assigned_tasks = len([t for t in self.schedule.tasks if t.assigned_resource is not None])
        return (assigned_tasks / total_tasks) * 100.0


@planning_solution
@dataclass
class Schedule:
    """
    TimeFold planning solution containing the complete schedule
    This is the main data structure that TimeFold optimizes
    """
    resources: Annotated[
        List[Resource], 
        ProblemFactCollectionProperty, 
        ValueRangeProvider(id='resourceRange')
    ]
    
    tasks: Annotated[
        List[Task], 
        PlanningEntityCollectionProperty
    ]
    
    locations: Annotated[
        List[Location], 
        ProblemFactCollectionProperty
    ] = field(default_factory=list)
    
    time_range: Annotated[
        List[datetime.datetime], 
        ProblemFactCollectionProperty, 
        ValueRangeProvider(id='timeRange')
    ] = field(default_factory=list)
    
    score: Annotated[
        Optional[HardSoftScore], 
        PlanningScore
    ] = field(default=None)
    
    def get_tasks_for_resource(self, resource: Resource) -> List[Task]:
        """Get all tasks assigned to a specific resource"""
        return [task for task in self.tasks if task.assigned_resource == resource]
    
    def get_unassigned_tasks(self) -> List[Task]:
        """Get all tasks that haven't been assigned to any resource"""
        return [task for task in self.tasks if task.assigned_resource is None]
    
    def get_resource_utilization(self, resource: Resource) -> float:
        """Calculate utilization percentage for a resource"""
        assigned_tasks = self.get_tasks_for_resource(resource)
        if not assigned_tasks:
            return 0.0
        
        total_minutes = sum(task.get_total_duration_minutes() for task in assigned_tasks)
        available_minutes = resource.max_daily_hours * 60
        
        return min(100.0, (total_minutes / available_minutes) * 100.0)
    
    def get_schedule_statistics(self) -> Dict[str, Any]:
        """Calculate comprehensive schedule statistics"""
        total_tasks = len(self.tasks)
        assigned_tasks = len([t for t in self.tasks if t.assigned_resource is not None])
        
        # Resource utilization
        utilizations = []
        for resource in self.resources:
            if resource.is_currently_available():
                utilizations.append(self.get_resource_utilization(resource))
        
        avg_utilization = sum(utilizations) / len(utilizations) if utilizations else 0.0
        
        # Priority distribution
        priority_counts = {}
        for task in self.tasks:
            priority = task.priority.name
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
        
        return {
            'total_tasks': total_tasks,
            'assigned_tasks': assigned_tasks,
            'unassigned_tasks': total_tasks - assigned_tasks,
            'assignment_rate': (assigned_tasks / total_tasks * 100) if total_tasks > 0 else 0,
            'average_utilization': avg_utilization,
            'resource_utilizations': {r.id: self.get_resource_utilization(r) for r in self.resources},
            'priority_distribution': priority_counts,
            'available_resources': len([r for r in self.resources if r.is_currently_available()]),
            'total_resources': len(self.resources)
        }


class SchedulingEngine(ABC):
    """
    Abstract base class for scheduling engines
    Domain-specific implementations should inherit from this class
    """
    
    def __init__(self, constraint_provider_class: Type[ConstraintProvider]):
        """
        Initialize the scheduling engine with a constraint provider
        
        Args:
            constraint_provider_class: Class that provides domain-specific constraints
        """
        self.constraint_provider_class = constraint_provider_class
        self.solver_factory = None
        self._initialize_solver()
    
    def _initialize_solver(self):
        """Initialize the TimeFold solver with configuration"""
        config = SolverConfig()
        
        # Set the solution class
        config = config.with_solution_class(Schedule)
        
        # Set entity classes (will be set by domain-specific implementations)
        config = config.with_entity_classes(self._get_entity_classes())
        
        # Set constraint provider
        config = config.with_constraint_provider_class(self.constraint_provider_class)
        
        # Set termination configuration
        config = config.with_termination_spent_limit(datetime.timedelta(seconds=30))
        
        self.solver_factory = SolverFactory.create(config)
    
    @abstractmethod
    def _get_entity_classes(self) -> List[Type]:
        """Get the planning entity classes for this domain"""
        pass
    
    def create_schedule(self, request: SchedulingRequest) -> SchedulingResult:
        """
        Create an optimized schedule from the given request
        
        Args:
            request: Scheduling request with resources, tasks, and constraints
            
        Returns:
            SchedulingResult with optimized schedule and statistics
        """
        # Create initial schedule
        schedule = self._create_initial_schedule(request)
        
        # Configure solver for this request
        solver = self._configure_solver_for_request(request)
        
        # Solve the scheduling problem
        start_time = datetime.datetime.now()
        optimized_schedule = solver.solve(schedule)
        end_time = datetime.datetime.now()
        
        optimization_time = (end_time - start_time).total_seconds()
        
        # Create result
        result = SchedulingResult(
            schedule=optimized_schedule,
            score=optimized_schedule.score,
            optimization_time_seconds=optimization_time,
            unassigned_tasks=optimized_schedule.get_unassigned_tasks()
        )
        
        # Calculate statistics
        result.statistics = optimized_schedule.get_schedule_statistics()
        
        # Generate recommendations
        result.recommendations = self._generate_recommendations(optimized_schedule)
        
        return result
    
    def _create_initial_schedule(self, request: SchedulingRequest) -> Schedule:
        """Create initial schedule from request data"""
        # Generate time range for planning variables
        time_range = self._generate_time_range(request.schedule_date)
        
        return Schedule(
            resources=request.resources,
            tasks=request.tasks,
            locations=request.locations,
            time_range=time_range
        )
    
    def _generate_time_range(self, schedule_date: datetime.date) -> List[datetime.datetime]:
        """Generate time slots for the scheduling day"""
        start_time = datetime.datetime.combine(schedule_date, datetime.time(6, 0))
        end_time = datetime.datetime.combine(schedule_date, datetime.time(22, 0))
        
        time_slots = []
        current_time = start_time
        
        # Generate 15-minute time slots
        while current_time <= end_time:
            time_slots.append(current_time)
            current_time += datetime.timedelta(minutes=15)
        
        return time_slots
    
    def _configure_solver_for_request(self, request: SchedulingRequest):
        """Configure solver based on request parameters"""
        solver = self.solver_factory.build_solver()
        
        # Update termination time if specified
        if request.optimization_time_seconds != 30:
            # Would reconfigure solver with new termination time
            pass
        
        return solver
    
    def _generate_recommendations(self, schedule: Schedule) -> List[str]:
        """Generate recommendations based on the optimized schedule"""
        recommendations = []
        
        # Check for unassigned high-priority tasks
        unassigned_urgent = [t for t in schedule.get_unassigned_tasks() 
                           if t.priority.value >= 4]
        if unassigned_urgent:
            recommendations.append(
                f"Consider hiring additional resources - {len(unassigned_urgent)} urgent tasks unassigned"
            )
        
        # Check for resource utilization
        low_utilization = [r for r in schedule.resources 
                          if schedule.get_resource_utilization(r) < 50]
        if low_utilization:
            recommendations.append(
                f"Consider redistributing work - {len(low_utilization)} resources under-utilized"
            )
        
        # Check for overutilization
        over_utilization = [r for r in schedule.resources 
                           if schedule.get_resource_utilization(r) > 90]
        if over_utilization:
            recommendations.append(
                f"Consider reducing workload - {len(over_utilization)} resources over-utilized"
            )
        
        return recommendations
    
    def update_schedule_real_time(self, current_schedule: Schedule, 
                                 changes: List[Dict[str, Any]]) -> SchedulingResult:
        """
        Update schedule in real-time based on changes
        
        Args:
            current_schedule: Current schedule to update
            changes: List of changes to apply (staff illness, task changes, etc.)
            
        Returns:
            Updated scheduling result
        """
        # Apply changes to the schedule
        updated_schedule = self._apply_changes(current_schedule, changes)
        
        # Re-optimize with shorter time limit for real-time response
        request = SchedulingRequest(
            resources=updated_schedule.resources,
            tasks=updated_schedule.tasks,
            locations=updated_schedule.locations,
            optimization_time_seconds=10  # Shorter time for real-time updates
        )
        
        return self.create_schedule(request)
    
    def _apply_changes(self, schedule: Schedule, changes: List[Dict[str, Any]]) -> Schedule:
        """Apply real-time changes to the schedule"""
        for change in changes:
            change_type = change.get('type')
            
            if change_type == 'STAFF_SICK':
                resource = change.get('resource')
                if resource:
                    resource.mark_sick(change.get('until'), change.get('reason', 'illness'))
            
            elif change_type == 'NO_SHOW':
                task = change.get('task')
                if task:
                    task.mark_no_show(change.get('reason', ''), change.get('reschedule', True))
            
            elif change_type == 'EXTENDED_TASK':
                task = change.get('task')
                additional_minutes = change.get('additional_minutes', 0)
                if task and additional_minutes:
                    task.extend_task(additional_minutes, change.get('reason', ''))
            
            elif change_type == 'PIN_TASK':
                task = change.get('task')
                if task:
                    if change.get('pin_assignment', False):
                        task.pin_assignment(change.get('reason', 'manual_pin'))
                    if change.get('pin_time', False):
                        task.pin_time(change.get('reason', 'manual_pin'))
        
        return schedule
