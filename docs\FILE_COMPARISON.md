# File Structure and Purpose Comparison

## Two Implementation Approaches

This project contains **two different implementations** of healthcare scheduling using TimeFold Solver:

### 1. **Simple Implementation** (Learning/Basic Use)

| File | Purpose | Complexity |
|------|---------|------------|
| `main.py` | Entry point for basic example | ⭐ Simple |
| `domain.py` | Basic domain model (Clinician, Visit, Schedule) | ⭐ Simple |
| `constraints.py` | Basic constraints (skill matching, overlaps) | ⭐ Simple |

**Use Cases:**
- Learning TimeFold Solver concepts
- Understanding constraint programming basics
- Quick prototyping
- Educational demonstrations

**Features:**
- Basic skill matching
- Time window constraints
- Simple dependency handling
- Minimal setup required

**Run Command:**
```bash
python main.py
```

---

### 2. **Advanced Implementation** (Production Use)

| File | Purpose | Complexity |
|------|---------|------------|
| `healthcare_main.py` | Entry point for advanced framework | ⭐⭐⭐ Advanced |
| `healthcare_domain.py` | Domain-agnostic healthcare entities | ⭐⭐⭐ Advanced |
| `healthcare_constraints.py` | Comprehensive healthcare constraints | ⭐⭐⭐ Advanced |
| `healthcare_scheduler.py` | Main scheduling engine | ⭐⭐⭐ Advanced |
| `google_maps_service.py` | Google Maps integration | ⭐⭐ Intermediate |

**Use Cases:**
- Production healthcare scheduling systems
- Home healthcare agencies
- Hospital staff scheduling
- Emergency response coordination
- Multi-facility operations

**Features:**
- Domain-agnostic design (adaptable to other domains)
- Hierarchical skill matching with substitution
- Google Maps integration for real travel times
- Real-time schedule updates
- Safety pairing requirements
- Infection control protocols
- Advanced analytics and reporting
- Emergency priority handling

**Run Command:**
```bash
export GOOGLE_API_KEY=your_api_key
python healthcare_main.py
```

---

## Feature Comparison

| Feature | Simple (`main.py`) | Advanced (`healthcare_main.py`) |
|---------|-------------------|----------------------------------|
| **Domain Model** | Basic classes | Abstract base classes + healthcare specialization |
| **Skills System** | Simple string matching | Hierarchical skills with substitution |
| **Location Handling** | Service areas only | Full geographic locations with Google Maps |
| **Travel Optimization** | ❌ Not included | ✅ Real-time Google Maps integration |
| **Real-time Updates** | ❌ Not supported | ✅ Dynamic schedule adjustments |
| **Safety Features** | ❌ Basic only | ✅ Safety pairing, infection control |
| **Analytics** | ❌ Minimal | ✅ Comprehensive reporting |
| **Emergency Handling** | ❌ Not supported | ✅ Priority-based emergency response |
| **Multi-facility** | ❌ Single area | ✅ Cross-facility coordination |
| **Configuration** | ❌ Hard-coded | ✅ Configurable for different scenarios |

---

## When to Use Which?

### Use **Simple Implementation** (`main.py`) when:
- 🎓 **Learning TimeFold Solver** - Understanding core concepts
- 🚀 **Quick Prototyping** - Testing basic scheduling ideas
- 📚 **Educational Purposes** - Teaching constraint programming
- 🔧 **Simple Requirements** - Basic scheduling without complex features
- ⚡ **Fast Setup** - Need something working immediately

### Use **Advanced Implementation** (`healthcare_main.py`) when:
- 🏥 **Production Systems** - Real healthcare scheduling applications
- 🌍 **Real-world Routing** - Need actual travel times and distances
- 🚨 **Emergency Response** - Handling urgent situations
- 🏢 **Multi-facility** - Coordinating across multiple locations
- 📊 **Advanced Analytics** - Need comprehensive reporting
- 🔄 **Real-time Updates** - Dynamic schedule adjustments
- 🛡️ **Safety Requirements** - Healthcare-specific safety protocols
- 🎯 **Domain Adaptation** - Want to adapt for other domains beyond healthcare

---

## Migration Path

If you start with the simple implementation and want to upgrade:

1. **Start with `main.py`** to understand TimeFold concepts
2. **Study the constraint patterns** in `constraints.py`
3. **Explore `healthcare_domain.py`** to see domain-agnostic design
4. **Review `healthcare_constraints.py`** for advanced constraint patterns
5. **Integrate Google Maps** using `google_maps_service.py`
6. **Use `healthcare_scheduler.py`** for production orchestration

---

## Code Examples

### Simple Implementation Example
```python
# main.py - Basic scheduling
from domain import Clinician, Visit, Schedule
import constraints

# Simple domain objects
alice = Clinician(1, "Alice", [Skill.SKILLED_NURSING], [service_area], [availability])
visit = Visit(1, "Patient A", Skill.SKILLED_NURSING, service_area, start, end, 60, False, [])

# Basic solver setup
solver_config = SolverConfig(
    solution_class=Schedule,
    entity_class_list=[Visit],
    score_director_factory_config=ScoreDirectorFactoryConfig(
        constraint_provider_function=constraints.define_constraints
    )
)
```

### Advanced Implementation Example
```python
# healthcare_main.py - Advanced framework
from healthcare_domain import HealthcareResource, HealthcareTask, Location
from healthcare_scheduler import HealthcareSchedulingEngine

# Advanced domain objects with rich attributes
alice = HealthcareResource(
    id="nurse_alice",
    name="Alice Johnson, RN",
    skills=[HealthcareSkills.WOUND_CARE_ADVANCED, HealthcareSkills.MEDICATION_IV],
    license_number="RN123456",
    certifications=["IV_CERTIFIED"],
    max_daily_hours=8,
    can_supervise=True
)

# Real geographic location
patient_home = Location(
    id="patient_home_1",
    latitude=40.7589,
    longitude=-73.9851,
    location_type=LocationType.PATIENT_HOME,
    safety_rating=4,
    accessibility_features=["wheelchair_accessible"]
)

# Advanced task with medical context
wound_care = HealthcareTask(
    id="wound_care_001",
    name="Advanced Wound Care",
    required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
    location=patient_home,
    priority=Priority.HIGH,
    clinical_notes="Complex diabetic ulcer",
    infection_control_level="CONTACT_PRECAUTIONS"
)

# Production-ready scheduler with Google Maps
scheduler = HealthcareSchedulingEngine(google_api_key="your_key")
result = scheduler.create_schedule(request)
```

---

## Summary

- **`main.py`** = Simple, educational, quick start
- **`healthcare_main.py`** = Advanced, production-ready, feature-rich

Both serve different purposes and can coexist in the same project. Start simple, then upgrade to advanced when you need the additional features!
