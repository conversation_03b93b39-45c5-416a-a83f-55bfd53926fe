# Legacy healthcare domain implementation - kept for backward compatibility
# This file will be moved to domains/healthcare/ in the new framework structure

import datetime
from typing import List, Set, Optional, Annotated
from dataclasses import dataclass, field
from timefold.solver.domain import planning_entity, planning_solution
from timefold.solver.domain import PlanningVariable, PlanningId, ValueRangeProvider
from timefold.solver.domain import ProblemFactCollectionProperty, PlanningEntityCollectionProperty, PlanningScore
from timefold.solver.score import HardSoftScore

class Skill:
    SKILLED_NURSING = "SKILLED_NURSING"
    PERSONAL_CARE = "PERSONAL_CARE"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    OCCUPATIONAL_THERAPY = "OCCUPATIONAL_THERAPY"

class ServiceArea:
    def __init__(self, code: str):
        self.code = code
    def __repr__(self):
        return self.code
    def __eq__(self, other):
        return isinstance(other, ServiceArea) and other.code == self.code
    def __hash__(self):
        return hash(self.code)

class AvailabilitySlot:
    def __init__(self, start: datetime.datetime, end: datetime.datetime):
        self.start = start
        self.end = end
    def contains_range(self, start: datetime.datetime, end: datetime.datetime):
        return self.start <= start and self.end >= end
    def __repr__(self):
        return f"{self.start}-{self.end}"

class Clinician:
    def __init__(self, id_: int, name: str, skills: Set[str],
                 service_areas: Set[ServiceArea],
                 availability: List[AvailabilitySlot],
                 max_daily_visits: int = 5):
        self.id = id_
        self.name = name
        self.skills = skills
        self.service_areas = service_areas
        self.availability = availability
        self.max_daily_visits = max_daily_visits
    def __repr__(self):
        return self.name
    def __eq__(self, other):
        return isinstance(other, Clinician) and other.id == self.id
    def __hash__(self):
        return hash(self.id)

class Dependency:
    def __init__(self, predecessor_id: int, same_clinician_required: bool):
        self.predecessor_id = predecessor_id
        self.same_clinician_required = same_clinician_required
    def __repr__(self):
        flag = "SAME" if self.same_clinician_required else "ANY"
        return f"{self.predecessor_id}({flag})"

@planning_entity
@dataclass
class Visit:
    id: Annotated[int, PlanningId]
    patient_name: str
    required_skill: str
    service_area: ServiceArea
    window_start: datetime.datetime
    window_end: datetime.datetime
    duration_minutes: int
    fixed_time: bool
    dependencies: List[Dependency]
    clinician: Annotated[Optional[Clinician], PlanningVariable(value_range_provider_refs=['clinicianRange'])] = field(default=None)
    start: Annotated[Optional[datetime.datetime], PlanningVariable(value_range_provider_refs=['timeRange'])] = field(default=None)

    def get_end(self):
        return self.start + datetime.timedelta(minutes=self.duration_minutes) if self.start else None

    # pinning filter via method
    def is_pinned(self):
        return self.fixed_time

    def __repr__(self):
        return f"{self.patient_name}@{self.start}"

@planning_solution
@dataclass
class Schedule:
    clinicians: Annotated[List[Clinician], ProblemFactCollectionProperty, ValueRangeProvider(id='clinicianRange')]
    visits: Annotated[List[Visit], PlanningEntityCollectionProperty]
    time_range: Annotated[List[datetime.datetime], ProblemFactCollectionProperty, ValueRangeProvider(id='timeRange')]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
