# TimeFold Python Quick Reference

## Essential Imports
```python
import datetime
from typing import List, Set, Optional, Annotated
from dataclasses import dataclass, field

from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver import SolverFactory
from timefold.solver.domain import planning_entity, planning_solution
from timefold.solver.domain import PlanningVariable, PlanningId, ValueRangeProvider
from timefold.solver.domain import ProblemFactCollectionProperty, PlanningEntityCollectionProperty, PlanningScore
from timefold.solver.score import HardSoftScore, constraint_provider, ConstraintCollectors, Joiners
```

## Domain Model Template

### Planning Solution
```python
@planning_solution
@dataclass
class Schedule:
    # Problem facts (immutable data)
    resources: Annotated[List[Resource], ProblemFactCollectionProperty, ValueRangeProvider(id='resourceRange')]
    time_slots: Annotated[List[datetime], ProblemFactCollectionProperty, ValueRangeProvider(id='timeRange')]
    
    # Planning entities (what gets optimized)
    tasks: Annotated[List[Task], PlanningEntityCollectionProperty]
    
    # Solution score
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
```

### Planning Entity
```python
@planning_entity
@dataclass
class Task:
    # Immutable properties
    id: Annotated[int, PlanningId]
    name: str
    duration: int
    
    # Planning variables (solver assigns these)
    resource: Annotated[Optional[Resource], PlanningVariable(value_range_provider_refs=['resourceRange'])] = field(default=None)
    start_time: Annotated[Optional[datetime], PlanningVariable(value_range_provider_refs=['timeRange'])] = field(default=None)
```

## Constraint Patterns

### Basic Constraint
```python
@constraint_provider
def define_constraints(constraint_factory):
    return [
        basic_constraint(constraint_factory),
        # ... other constraints
    ]

def basic_constraint(cf):
    return (cf.for_each(EntityClass)
            .filter(violation_condition)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Constraint Name'))
```

### Common Constraint Types

#### 1. Resource Conflict
```python
def resource_conflict(cf):
    def same_resource_overlap(t1, t2):
        return (t1.resource == t2.resource and t1.resource is not None and
                t1.start_time is not None and t2.start_time is not None and
                not (t1.start_time + timedelta(minutes=t1.duration) <= t2.start_time or
                     t2.start_time + timedelta(minutes=t2.duration) <= t1.start_time))
    
    return (cf.for_each_unique_pair(Task)
            .filter(same_resource_overlap)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Resource conflict'))
```

#### 2. Capacity Constraint
```python
def capacity_constraint(cf):
    return (cf.for_each(Task)
            .group_by(lambda t: t.resource, ConstraintCollectors.count())
            .filter(lambda resource, count: resource is not None and count > resource.capacity)
            .penalize(HardSoftScore.ONE_HARD, lambda resource, count: count - resource.capacity)
            .as_constraint('Capacity exceeded'))
```

#### 3. Time Window
```python
def time_window_constraint(cf):
    return (cf.for_each(Task)
            .filter(lambda t: t.start_time is not None and
                    (t.start_time < t.earliest_start or 
                     t.start_time + timedelta(minutes=t.duration) > t.latest_end))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Time window violation'))
```

#### 4. Dependency Order
```python
def dependency_constraint(cf):
    def is_predecessor(t1, t2):
        return t1.id in t2.predecessor_ids
    
    def order_violated(pre, post):
        return (pre.start_time is not None and post.start_time is not None and
                post.start_time < pre.start_time + timedelta(minutes=pre.duration))
    
    return (cf.for_each(Task)
            .join(Task, Joiners.filtering(is_predecessor))
            .filter(order_violated)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order'))
```

## Solver Configuration

### Basic Configuration
```python
solver_config = SolverConfig(
    solution_class=Schedule,
    entity_class_list=[Task],
    score_director_factory_config=ScoreDirectorFactoryConfig(
        constraint_provider_function=define_constraints
    ),
    termination_config=TerminationConfig(
        spent_limit=Duration(seconds=30)
    )
)

solver_factory = SolverFactory.create(solver_config)
solver = solver_factory.build_solver()
solution = solver.solve(problem)
```

### Advanced Termination
```python
termination_config = TerminationConfig(
    spent_limit=Duration(seconds=60),                    # Max time
    best_score_limit=HardSoftScore.ZERO,               # Stop when perfect
    unimproved_spent_limit=Duration(seconds=10),       # Stop if no improvement
    step_count_limit=10000                              # Max iterations
)
```

## Score Types

### HardSoftScore
```python
# Perfect solution
HardSoftScore.ZERO                    # 0hard/0soft

# Hard constraint violations (invalid)
HardSoftScore.ONE_HARD               # -1hard/0soft
HardSoftScore.of(-5, 0)              # -5hard/0soft

# Soft constraint violations (suboptimal but valid)
HardSoftScore.ONE_SOFT               # 0hard/-1soft
HardSoftScore.of(0, -10)             # 0hard/-10soft

# Mixed
HardSoftScore.of(-2, -5)             # -2hard/-5soft (invalid)
```

## Common Patterns

### Null Safety
```python
def safe_constraint(cf):
    return (cf.for_each(Task)
            .filter(lambda t: t.resource is not None)      # Always check nulls first
            .filter(lambda t: t.start_time is not None)
            .filter(lambda t: other_condition(t))
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Safe constraint'))
```

### Custom Scoring
```python
def weighted_constraint(cf):
    def calculate_penalty(task):
        if task.priority == 'HIGH':
            return 10
        elif task.priority == 'MEDIUM':
            return 5
        else:
            return 1
    
    return (cf.for_each(Task)
            .filter(violation_condition)
            .penalize(HardSoftScore.ONE_SOFT, calculate_penalty)
            .as_constraint('Weighted penalty'))
```

### Conditional Logic
```python
def conditional_constraint(cf):
    return (cf.for_each(Task)
            .filter(lambda t: t.task_type == 'URGENT')     # Only apply to urgent tasks
            .filter(lambda t: t.start_time is not None and 
                    t.start_time.hour > 17)                # After 5 PM
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Urgent tasks after hours'))
```

## Debugging Tips

### Add Logging
```python
def debug_constraint(cf):
    def debug_filter(task):
        violation = task.resource is None
        if violation:
            print(f"DEBUG: Unassigned task: {task.name}")
        return violation
    
    return (cf.for_each(Task)
            .filter(debug_filter)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Unassigned tasks'))
```

### Validate Solution
```python
def validate_solution(solution):
    print(f"Final score: {solution.score}")
    
    unassigned = [t for t in solution.tasks if t.resource is None]
    if unassigned:
        print(f"Unassigned tasks: {[t.name for t in unassigned]}")
    
    for task in solution.tasks:
        if task.resource and task.start_time:
            print(f"{task.name}: {task.resource.name} at {task.start_time}")
```

## Performance Optimization

### Efficient Filtering
```python
# Good: Filter cheap conditions first
def efficient_constraint(cf):
    return (cf.for_each(Task)
            .filter(lambda t: t.resource is not None)      # Quick null check
            .filter(lambda t: t.start_time is not None)    # Quick null check
            .filter(lambda t: expensive_condition(t))      # Expensive check last
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Efficient'))
```

### Limit Value Ranges
```python
# Generate reasonable time slots (not every second)
def generate_time_slots():
    slots = []
    start = datetime.datetime(2025, 6, 1, 8, 0)
    for i in range(40):  # 8 AM to 6 PM, every 15 minutes
        slots.append(start + datetime.timedelta(minutes=15 * i))
    return slots
```

## Common Mistakes to Avoid

1. **Forgetting null checks** in constraints
2. **Using too fine time granularity** (seconds instead of minutes)
3. **Missing value range providers** in planning variables
4. **Not calling constraint functions** in constraint provider
5. **Expensive operations in filters** without early filtering
6. **Forgetting to register constraint provider** in solver config
7. **Using mutable objects** as problem facts

## File Structure Template
```
project/
├── domain.py          # Domain classes (@planning_entity, @planning_solution)
├── constraints.py     # Constraint definitions (@constraint_provider)
├── main.py           # Solver configuration and execution
├── data.py           # Data loading and problem building
└── requirements.txt  # Dependencies (timefold==1.22.1b0)
```
