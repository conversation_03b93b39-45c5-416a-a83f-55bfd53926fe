# Healthcare Scheduling Examples

This guide provides practical examples of using the domain-agnostic healthcare scheduling framework for various real-world scenarios.

## Table of Contents

1. [Basic Home Healthcare Scheduling](#basic-home-healthcare-scheduling)
2. [Emergency Home Visit Coordination](#emergency-home-visit-coordination)
3. [Multi-Region Home Care Operations](#multi-region-home-care-operations)
4. [Specialized Home Care Programs](#specialized-home-care-programs)
5. [Real-Time Schedule Updates](#real-time-schedule-updates)
6. [Custom Constraint Configuration](#custom-constraint-configuration)

## Basic Home Healthcare Scheduling

### Scenario
A home healthcare agency needs to schedule nurses for patient visits across a metropolitan area, considering travel time, skill requirements, and patient preferences.

### Implementation

```python
import datetime
from healthcare_domain import *
from healthcare_scheduler import HealthcareSchedulingEngine, SchedulingRequest

# 1. Define Locations
locations = [
    Location(
        id="patient_home_1",
        name="Mrs. <PERSON>'s Home",
        latitude=40.7589,
        longitude=-73.9851,
        location_type=LocationType.PATIENT_HOME,
        address="456 Oak Ave, New York, NY",
        safety_rating=4,
        accessibility_features=["ground_floor", "wheelchair_ramp"],
        parking_available=True
    ),
    Location(
        id="patient_home_2",
        name="Mr<PERSON> <PERSON>'s Apartment",
        latitude=40.7282,
        longitude=-73.7949,
        location_type=LocationType.PATIENT_HOME,
        address="789 Pine St, Queens, NY",
        safety_rating=3,
        accessibility_features=[],
        parking_available=False
    )
]

# 2. Define Healthcare Resources (Nurses)
resources = [
    HealthcareResource(
        id="nurse_alice",
        name="Alice Johnson, RN",
        skills=[
            HealthcareSkills.WOUND_CARE_ADVANCED,
            HealthcareSkills.MEDICATION_IV,
            HealthcareSkills.MEDICATION_BASIC
        ],
        availability_windows=[
            TimeWindow(
                start=datetime.datetime(2025, 1, 15, 8, 0),
                end=datetime.datetime(2025, 1, 15, 16, 0)
            )
        ],
        license_number="RN123456",
        max_daily_hours=8,
        can_supervise=True
    ),
    HealthcareResource(
        id="nurse_bob",
        name="Bob Smith, LPN",
        skills=[
            HealthcareSkills.WOUND_CARE_BASIC,
            HealthcareSkills.MEDICATION_BASIC
        ],
        availability_windows=[
            TimeWindow(
                start=datetime.datetime(2025, 1, 15, 12, 0),
                end=datetime.datetime(2025, 1, 15, 20, 0)
            )
        ],
        license_number="LPN789012",
        max_daily_hours=8,
        requires_supervision=True
    )
]

# 3. Define Healthcare Tasks (Patient Visits)
tasks = [
    HealthcareTask(
        id="wound_care_johnson",
        name="Advanced Wound Care - Mrs. Johnson",
        duration_minutes=90,
        required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
        time_windows=[
            TimeWindow(
                start=datetime.datetime(2025, 1, 15, 9, 0),
                end=datetime.datetime(2025, 1, 15, 12, 0),
                is_preferred=True
            )
        ],
        location=locations[0],
        priority=Priority.HIGH,
        patient_id="patient_johnson",
        service_type=HealthcareServiceTypes.WOUND_CARE,
        clinical_notes="Complex diabetic ulcer requiring advanced care"
    ),
    HealthcareTask(
        id="medication_smith",
        name="Medication Administration - Mr. Smith",
        duration_minutes=45,
        required_skills=[HealthcareSkills.MEDICATION_BASIC],
        time_windows=[
            TimeWindow(
                start=datetime.datetime(2025, 1, 15, 13, 0),
                end=datetime.datetime(2025, 1, 15, 17, 0)
            )
        ],
        location=locations[1],
        priority=Priority.ROUTINE,
        patient_id="patient_smith",
        service_type=HealthcareServiceTypes.MEDICATION_ADMIN
    )
]

# 4. Create and Execute Schedule
scheduler = HealthcareSchedulingEngine(google_api_key="your_api_key")

request = SchedulingRequest(
    resources=resources,
    tasks=tasks,
    locations=locations,
    schedule_date=datetime.date(2025, 1, 15),
    optimization_time_seconds=30
)

result = scheduler.create_schedule(request)

# 5. Display Results
print(f"Schedule Score: {result.score}")
print(f"Assignment Rate: {result.statistics['assignment_rate']:.1f}%")

for resource in resources:
    resource_tasks = result.schedule.get_tasks_for_resource(resource)
    if resource_tasks:
        print(f"\n{resource.name}:")
        for task in sorted(resource_tasks, key=lambda t: t.start_time or datetime.datetime.min):
            if task.start_time:
                print(f"  {task.start_time.strftime('%H:%M')} - {task.name}")
```

## Emergency Response Coordination

### Scenario
A healthcare system needs to handle emergency calls while maintaining scheduled visits, requiring real-time optimization and priority-based scheduling.

### Implementation

```python
# 1. Define Emergency Task
emergency_task = HealthcareTask(
    id="emergency_001",
    name="Critical Assessment - Emergency",
    duration_minutes=120,
    required_skills=[
        HealthcareSkills.WOUND_CARE_ADVANCED,
        HealthcareSkills.MEDICATION_IV
    ],
    time_windows=[
        TimeWindow(
            start=datetime.datetime.now(),
            end=datetime.datetime.now() + datetime.timedelta(hours=2),
            flexibility_minutes=0  # No flexibility for emergencies
        )
    ],
    location=Location(
        id="emergency_location",
        name="Emergency Site",
        latitude=40.7831,
        longitude=-73.9712,
        location_type=LocationType.EMERGENCY,
        address="Emergency Location",
        safety_rating=2  # Potentially unsafe
    ),
    priority=Priority.CRITICAL,
    patient_id="emergency_patient",
    service_type=HealthcareServiceTypes.EMERGENCY_RESPONSE,
    clinical_notes="Critical wound dehiscence requiring immediate attention"
)

# 2. Configure for Emergency Response
emergency_config = ConstraintConfiguration(
    minimize_travel=ConstraintWeight("0hard", "50soft"),  # Extremely important
    prefer_continuity=ConstraintWeight("0hard", "1soft"),  # Less important
    safety_pairing=ConstraintWeight("1hard")  # Required for emergencies
)

# 3. Real-Time Schedule Update
current_schedule = get_current_active_schedule()  # Your existing schedule

changes = [
    {
        "type": "ADD_EMERGENCY_TASK",
        "task": emergency_task,
        "priority_override": True
    }
]

# 4. Update Schedule in Real-Time
emergency_result = scheduler.update_schedule_real_time(
    current_schedule=current_schedule,
    changes=changes
)

# 5. Handle Emergency Assignment
if emergency_result.schedule.get_unassigned_tasks():
    print("⚠️ Emergency task could not be assigned!")
    # Trigger escalation protocol
    recommendations = scheduler.get_schedule_recommendations(
        emergency_result.schedule,
        emergency_task
    )
    
    print("Emergency Assignment Recommendations:")
    for rec in recommendations[:3]:
        print(f"- {rec['resource_name']} at {rec['start_time']} "
              f"(Impact: {rec['impact_score']:.1f})")
```

## Multi-Facility Hospital Scheduling

### Scenario
A hospital system with multiple facilities needs to coordinate staff across locations, considering travel time between facilities and specialized equipment availability.

### Implementation

```python
# 1. Define Multiple Facilities
facilities = [
    Location(
        id="main_hospital",
        name="Metropolitan General Hospital",
        latitude=40.7831,
        longitude=-73.9712,
        location_type=LocationType.HOSPITAL,
        address="321 Hospital Dr, New York, NY",
        timezone="America/New_York",
        accessibility_features=["wheelchair_accessible", "emergency_access"]
    ),
    Location(
        id="outpatient_clinic",
        name="Downtown Outpatient Clinic",
        latitude=40.7128,
        longitude=-74.0060,
        location_type=LocationType.CLINIC,
        address="123 Main St, New York, NY",
        timezone="America/New_York",
        accessibility_features=["wheelchair_accessible"]
    ),
    Location(
        id="specialty_center",
        name="Rehabilitation Center",
        latitude=40.7589,
        longitude=-73.9851,
        location_type=LocationType.CLINIC,
        address="456 Rehab Ave, New York, NY",
        timezone="America/New_York",
        accessibility_features=["wheelchair_accessible", "therapy_equipment"]
    )
]

# 2. Define Mobile Resources
mobile_resources = [
    HealthcareResource(
        id="mobile_nurse_sarah",
        name="Sarah Davis, RN - Mobile",
        skills=[
            HealthcareSkills.MEDICATION_IV,
            HealthcareSkills.WOUND_CARE_BASIC,
            HealthcareSkills.PHYSICAL_THERAPY
        ],
        availability_windows=[
            TimeWindow(
                start=datetime.datetime(2025, 1, 15, 7, 0),
                end=datetime.datetime(2025, 1, 15, 19, 0)
            )
        ],
        location=None,  # Mobile - can work at any facility
        max_daily_hours=10,
        specializations=["MOBILE_CARE", "MULTI_FACILITY"]
    ),
    HealthcareResource(
        id="specialist_therapist",
        name="Dr. Michael Chen, PT",
        skills=[HealthcareSkills.PHYSICAL_THERAPY],
        availability_windows=[
            TimeWindow(
                start=datetime.datetime(2025, 1, 15, 8, 0),
                end=datetime.datetime(2025, 1, 15, 17, 0)
            )
        ],
        location=facilities[2],  # Based at rehab center
        max_daily_hours=8,
        specializations=["ORTHOPEDIC_REHAB", "NEUROLOGICAL_REHAB"]
    )
]

# 3. Define Cross-Facility Tasks
cross_facility_tasks = [
    HealthcareTask(
        id="hospital_consultation",
        name="Specialist Consultation - Main Hospital",
        duration_minutes=60,
        required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
        location=facilities[0],  # Main hospital
        priority=Priority.HIGH,
        service_type="CONSULTATION",
        equipment_required=["specialized_assessment_tools"]
    ),
    HealthcareTask(
        id="outpatient_therapy",
        name="Outpatient Therapy Session",
        duration_minutes=90,
        required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
        location=facilities[1],  # Outpatient clinic
        priority=Priority.ROUTINE,
        service_type="THERAPY_SESSION"
    ),
    HealthcareTask(
        id="rehab_assessment",
        name="Comprehensive Rehab Assessment",
        duration_minutes=120,
        required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
        location=facilities[2],  # Rehab center
        priority=Priority.HIGH,
        service_type="ASSESSMENT",
        equipment_required=["gait_analysis", "strength_testing"]
    )
]

# 4. Configure for Multi-Facility Operations
multi_facility_config = ConstraintConfiguration(
    minimize_travel=ConstraintWeight("0hard", "15soft"),  # Important for efficiency
    balance_workload=ConstraintWeight("0hard", "10soft"),
    prefer_continuity=ConstraintWeight("0hard", "5soft")  # Less critical across facilities
)

# 5. Create Multi-Facility Schedule
multi_facility_request = SchedulingRequest(
    resources=mobile_resources,
    tasks=cross_facility_tasks,
    locations=facilities,
    schedule_date=datetime.date(2025, 1, 15),
    constraint_config=multi_facility_config,
    optimization_time_seconds=45,  # More time for complex scheduling
    include_travel_optimization=True
)

result = scheduler.create_schedule(multi_facility_request)

# 6. Analyze Cross-Facility Travel
if result.travel_summary:
    print("Multi-Facility Travel Analysis:")
    print(f"Total Distance: {result.travel_summary['total_distance_km']:.1f} km")
    print(f"Total Travel Time: {result.travel_summary['total_duration_hours']:.1f} hours")
    
    for resource_id, summary in result.travel_summary['resource_summaries'].items():
        print(f"{resource_id}: {summary['distance_km']:.1f} km, "
              f"${summary['estimated_fuel_cost']:.2f}")
```

## Specialized Care Programs

### Scenario
A rehabilitation program requires coordinated care from multiple disciplines (PT, OT, nursing) with specific sequencing and equipment requirements.

### Implementation

```python
# 1. Define Interdisciplinary Team
rehab_team = [
    HealthcareResource(
        id="pt_specialist",
        name="Dr. Lisa Wong, PT",
        skills=[HealthcareSkills.PHYSICAL_THERAPY],
        specializations=["STROKE_REHAB", "MOBILITY_TRAINING"],
        can_supervise=True
    ),
    HealthcareResource(
        id="ot_specialist", 
        name="James Miller, OT",
        skills=[HealthcareSkills.OCCUPATIONAL_THERAPY],
        specializations=["ADL_TRAINING", "COGNITIVE_REHAB"]
    ),
    HealthcareResource(
        id="rehab_nurse",
        name="Maria Rodriguez, RN",
        skills=[
            HealthcareSkills.MEDICATION_BASIC,
            HealthcareSkills.WOUND_CARE_BASIC
        ],
        specializations=["REHAB_NURSING", "PATIENT_EDUCATION"]
    )
]

# 2. Define Coordinated Care Tasks
rehab_program_tasks = [
    HealthcareTask(
        id="initial_assessment",
        name="Initial PT Assessment",
        duration_minutes=90,
        required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
        priority=Priority.HIGH,
        service_type="INITIAL_ASSESSMENT",
        clinical_notes="Baseline mobility and strength assessment",
        equipment_required=["assessment_tools", "gait_trainer"]
    ),
    HealthcareTask(
        id="ot_evaluation",
        name="Occupational Therapy Evaluation", 
        duration_minutes=75,
        required_skills=[HealthcareSkills.OCCUPATIONAL_THERAPY],
        priority=Priority.HIGH,
        service_type="OT_EVALUATION",
        dependencies=["initial_assessment"],  # Must follow PT assessment
        equipment_required=["ADL_simulation_tools"]
    ),
    HealthcareTask(
        id="nursing_education",
        name="Patient/Family Education",
        duration_minutes=60,
        required_skills=[HealthcareSkills.MEDICATION_BASIC],
        priority=Priority.ROUTINE,
        service_type="PATIENT_EDUCATION",
        dependencies=["initial_assessment", "ot_evaluation"],
        continuity_requirements=["interdisciplinary_coordination"]
    ),
    HealthcareTask(
        id="mobility_training",
        name="Mobility Training Session",
        duration_minutes=60,
        required_skills=[HealthcareSkills.PHYSICAL_THERAPY],
        priority=Priority.ROUTINE,
        service_type="THERAPY_SESSION",
        dependencies=["initial_assessment"],
        equipment_required=["parallel_bars", "mobility_aids"]
    )
]

# 3. Configure for Coordinated Care
coordinated_care_config = ConstraintConfiguration(
    prefer_continuity=ConstraintWeight("0hard", "20soft"),  # Very important
    balance_workload=ConstraintWeight("0hard", "15soft"),
    minimize_travel=ConstraintWeight("0hard", "5soft")  # Less important within facility
)

# 4. Add Equipment Coordination Constraint
def equipment_availability_constraint(cf):
    """Custom constraint for equipment scheduling"""
    def equipment_conflict(task1, task2):
        if (task1.start_time is None or task2.start_time is None or
            not task1.equipment_required or not task2.equipment_required):
            return False
        
        # Check for equipment overlap
        shared_equipment = set(task1.equipment_required) & set(task2.equipment_required)
        if shared_equipment:
            # Check time overlap
            task1_end = task1.get_end_time()
            task2_end = task2.get_end_time()
            
            return not (task1_end <= task2.start_time or task2_end <= task1.start_time)
        
        return False
    
    return (cf.for_each_unique_pair(HealthcareTask)
            .filter(equipment_conflict)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Equipment availability conflict'))

# 5. Execute Coordinated Scheduling
coordinated_request = SchedulingRequest(
    resources=rehab_team,
    tasks=rehab_program_tasks,
    locations=[facilities[2]],  # Rehab center
    constraint_config=coordinated_care_config,
    optimization_time_seconds=60
)

result = scheduler.create_schedule(coordinated_request)

# 6. Validate Care Coordination
print("Coordinated Care Schedule:")
for task in sorted(result.schedule.tasks, key=lambda t: t.start_time or datetime.datetime.min):
    if task.start_time and task.assigned_resource:
        print(f"{task.start_time.strftime('%H:%M')} - {task.name} "
              f"({task.assigned_resource.name})")
        if task.dependencies:
            print(f"  Dependencies: {', '.join(task.dependencies)}")
        if task.equipment_required:
            print(f"  Equipment: {', '.join(task.equipment_required)}")
```

## Real-Time Schedule Updates

### Scenario
Handle dynamic changes during the day: staff calling in sick, emergency visits, traffic delays, and patient cancellations.

### Implementation

```python
# 1. Simulate Real-Time Changes
real_time_changes = [
    {
        "type": "STAFF_UNAVAILABLE",
        "resource_id": "nurse_alice",
        "reason": "illness",
        "effective_time": datetime.datetime.now(),
        "duration_hours": 8
    },
    {
        "type": "TRAFFIC_DELAY",
        "resource_id": "nurse_bob", 
        "current_location": {"lat": 40.7128, "lng": -74.0060},
        "delay_minutes": 25,
        "affected_tasks": ["visit_002", "visit_003"]
    },
    {
        "type": "PATIENT_CANCELLATION",
        "task_id": "routine_visit_001",
        "reason": "patient_unavailable",
        "reschedule_required": True
    },
    {
        "type": "ADD_URGENT_VISIT",
        "task": HealthcareTask(
            id="urgent_visit_001",
            name="Urgent Wound Assessment",
            duration_minutes=75,
            required_skills=[HealthcareSkills.WOUND_CARE_ADVANCED],
            priority=Priority.URGENT,
            time_windows=[
                TimeWindow(
                    start=datetime.datetime.now(),
                    end=datetime.datetime.now() + datetime.timedelta(hours=4)
                )
            ]
        )
    }
]

# 2. Process Real-Time Updates
for change in real_time_changes:
    print(f"Processing change: {change['type']}")
    
    # Update schedule with minimal disruption
    updated_result = scheduler.update_schedule_real_time(
        current_schedule=current_schedule,
        changes=[change]
    )
    
    # Analyze impact
    impact_analysis = {
        "affected_tasks": len([t for t in updated_result.schedule.tasks 
                              if t.assigned_resource != original_assignment.get(t.id)]),
        "new_unassigned": len(updated_result.schedule.get_unassigned_tasks()),
        "score_change": updated_result.score.soft_score - original_score.soft_score
    }
    
    print(f"Impact: {impact_analysis}")
    
    # Generate notifications for affected patients
    if impact_analysis["affected_tasks"] > 0:
        notifications = generate_patient_notifications(updated_result.schedule)
        for notification in notifications:
            print(f"Notify: {notification}")

# 3. Monitor and Adjust
def continuous_monitoring():
    """Continuous monitoring and adjustment loop"""
    while True:
        # Check for traffic updates
        traffic_updates = scheduler.google_maps_service.get_real_time_traffic_updates(
            get_active_routes()
        )
        
        if traffic_updates:
            for resource_id, update in traffic_updates.items():
                if update["requires_reschedule"]:
                    # Automatic rescheduling for significant delays
                    scheduler.update_schedule_real_time(
                        current_schedule,
                        [{"type": "TRAFFIC_DELAY", "update": update}]
                    )
        
        # Sleep for monitoring interval
        time.sleep(300)  # Check every 5 minutes
```

This examples guide demonstrates the flexibility and power of the domain-agnostic healthcare scheduling framework across various real-world scenarios, from basic home healthcare to complex multi-facility operations and real-time emergency response.
