# Technical Implementation Guide

## Deep Dive into TimeFold Implementation

This guide provides detailed technical explanations for Java developers learning TimeFold in Python.

## 1. Understanding Planning Annotations

### @planning_solution
```python
@planning_solution
@dataclass
class Schedule:
    clinicians: Annotated[List[Clinician], ProblemFactCollectionProperty, ValueRange<PERSON>rovider(id='clinicianRange')]
    visits: Annotated[List[Visit], PlanningEntityCollectionProperty]
    time_range: Annotated[List[datetime], ProblemFactCollectionProperty, ValueRangeProvider(id='timeRange')]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
```

**Breakdown**:
- `@planning_solution`: Marks this as the root container for the optimization problem
- `ProblemFactCollectionProperty`: Immutable data that constraints can reference
- `PlanningEntityCollectionProperty`: Objects that will be modified during optimization
- `ValueRangeProvider`: Defines possible values for planning variables
- `PlanningScore`: Where the solver stores the solution quality

**Java Equivalent**:
```java
@PlanningSolution
public class Schedule {
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "clinicianRange")
    private List<Clinician> clinicians;

    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "timeRange")
    private List<LocalDateTime> timeRange;

    @PlanningEntityCollectionProperty
    private List<Visit> visits;

    @PlanningScore
    private HardSoftScore score;

    // Constructors, getters, setters...
}
```

**Why the time range is essential**:
- The `timeRange` provides possible values for the `start` planning variable in `Visit`
- Without it, the solver wouldn't know what time slots are available
- Both `clinicianRange` and `timeRange` are referenced in the `Visit` entity's planning variables

### @planning_entity
```python
@planning_entity
@dataclass
class Visit:
    # Problem facts (immutable)
    id: Annotated[int, PlanningId]
    patient_name: str
    required_skill: str
    # ... other immutable fields
    
    # Planning variables (what solver optimizes)
    clinician: Annotated[Optional[Clinician], PlanningVariable(value_range_provider_refs=['clinicianRange'])] = field(default=None)
    start: Annotated[Optional[datetime], PlanningVariable(value_range_provider_refs=['timeRange'])] = field(default=None)
```

**Key Points**:
- `@PlanningId`: Unique identifier for the entity
- `@PlanningVariable`: Fields the solver can modify
- `value_range_provider_refs`: Links to the possible values defined in the solution class

## 2. Constraint Provider Deep Dive

### Constraint Factory Pattern
```python
@constraint_provider
def define_constraints(constraint_factory):
    return [
        skill_mismatch(constraint_factory),
        service_area_mismatch(constraint_factory),
        # ... other constraints
    ]
```

**How it works**:
1. TimeFold calls this function during solver initialization
2. Each constraint function receives a `constraint_factory`
3. The factory provides methods to build constraint streams
4. Returns a list of all constraints to be evaluated

### Constraint Stream API

#### Basic Pattern
```python
def constraint_name(cf):
    return (cf.for_each(EntityClass)           # 1. Select entities
            .filter(condition_function)        # 2. Filter violations
            .penalize(score_impact)            # 3. Apply penalty
            .as_constraint('Constraint Name')) # 4. Name the constraint
```

#### Advanced Patterns

**1. Joining Multiple Entities**
```python
def dependency_order(cf):
    def is_dependency(pre, post):
        return any(d.predecessor_id == pre.id for d in post.dependencies)

    def order_violated(pre, post):
        return (pre.start is not None and post.start is not None and 
                post.start < pre.start + timedelta(minutes=pre.duration_minutes))

    return (cf.for_each(Visit)                    # Primary entity
            .join(Visit,                          # Join with same entity type
                  Joiners.filtering(is_dependency)) # Join condition
            .filter(order_violated)               # Violation condition
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Dependency order'))
```

**2. Grouping and Aggregation**
```python
def max_visits_per_clinician(cf):
    return (cf.for_each(Visit)
            .group_by(lambda v: v.clinician,                    # Group by clinician
                      ConstraintCollectors.count())             # Count visits per group
            .filter(lambda c, count: c is not None and count > c.max_daily_visits)
            .penalize(HardSoftScore.ONE_HARD,
                      lambda c, count: count - c.max_daily_visits)  # Penalty = excess visits
            .as_constraint('Clinician overbooked'))
```

**3. Unique Pairs**
```python
def clinician_overlap(cf):
    def overlap(v1, v2):
        if v1.clinician != v2.clinician or v1.start is None or v2.start is None:
            return False
        return not (v1.start + timedelta(minutes=v1.duration_minutes) <= v2.start or
                    v2.start + timedelta(minutes=v2.duration_minutes) <= v1.start)

    return (cf.for_each_unique_pair(Visit)      # Avoids checking (A,B) and (B,A)
            .filter(overlap)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Visits overlap'))
```

## 3. Score Calculation

### HardSoftScore Explained
```python
# Perfect solution
HardSoftScore.ZERO  # 0hard/0soft

# Hard constraint violations (invalid solutions)
HardSoftScore.ONE_HARD  # -1hard/0soft
HardSoftScore.of(-3, 0)  # -3hard/0soft

# Soft constraint violations (valid but suboptimal)
HardSoftScore.ONE_SOFT  # 0hard/-1soft
HardSoftScore.of(0, -5)  # 0hard/-5soft

# Mixed violations
HardSoftScore.of(-2, -10)  # -2hard/-10soft (invalid solution)
```

**Scoring Rules**:
1. **Hard constraints** must be satisfied (score ≥ 0)
2. **Soft constraints** are optimization goals (higher is better)
3. Hard score takes precedence over soft score
4. Solution with hard violations is always worse than one without

### Custom Scoring
```python
def custom_penalty(cf):
    return (cf.for_each(Visit)
            .filter(some_condition)
            .penalize(HardSoftScore.ONE_SOFT,
                      lambda visit: visit.duration_minutes)  # Penalty proportional to duration
            .as_constraint('Custom penalty'))
```

## 4. Solver Configuration Deep Dive

### Basic Configuration
```python
solver_config = SolverConfig(
    solution_class=Schedule,                    # Root solution class
    entity_class_list=[Visit],                  # Entities to optimize
    score_director_factory_config=ScoreDirectorFactoryConfig(
        constraint_provider_function=constraints.define_constraints
    ),
    termination_config=TerminationConfig(
        spent_limit=Duration(seconds=10)
    )
)
```

### Advanced Configuration Options

#### Termination Strategies
```python
termination_config = TerminationConfig(
    # Time-based termination
    spent_limit=Duration(seconds=30),
    
    # Score-based termination (stop when perfect solution found)
    best_score_limit=HardSoftScore.ZERO,
    
    # Improvement-based termination
    unimproved_spent_limit=Duration(seconds=5),
    
    # Step-based termination
    step_count_limit=1000
)
```

#### Construction Heuristics
```python
from timefold.solver.config import ConstructionHeuristicPhaseConfig

construction_config = ConstructionHeuristicPhaseConfig(
    construction_heuristic_type=ConstructionHeuristicType.FIRST_FIT_DECREASING
)
```

#### Local Search Configuration
```python
from timefold.solver.config import LocalSearchPhaseConfig

local_search_config = LocalSearchPhaseConfig(
    local_search_type=LocalSearchType.TABU_SEARCH,
    acceptance_config=AcceptorConfig(
        entity_tabu_size=7
    )
)
```

## 5. Problem Modeling Best Practices

### 1. **Immutable vs Mutable Design**
```python
# GOOD: Immutable problem facts
@dataclass(frozen=True)
class ServiceArea:
    code: str

# GOOD: Mutable planning variables
@planning_entity
@dataclass
class Visit:
    # Immutable facts
    id: int
    patient_name: str
    
    # Mutable variables
    clinician: Optional[Clinician] = field(default=None)
    start: Optional[datetime] = field(default=None)
```

### 2. **Efficient Value Ranges**
```python
# GOOD: Discrete time slots
time_range = [datetime(2025, 6, 1, 8, 0) + timedelta(minutes=15*i) 
              for i in range(40)]  # 8:00 AM to 5:45 PM

# AVOID: Continuous time (too many possibilities)
# time_range = [datetime(2025, 6, 1, 8, 0) + timedelta(seconds=i) 
#               for i in range(36000)]  # Every second
```

### 3. **Constraint Performance**
```python
# GOOD: Early filtering
def efficient_constraint(cf):
    return (cf.for_each(Visit)
            .filter(lambda v: v.clinician is not None)  # Filter nulls early
            .filter(lambda v: v.start is not None)
            .filter(expensive_condition)               # Expensive check last
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Efficient constraint'))

# AVOID: Late filtering
def inefficient_constraint(cf):
    return (cf.for_each(Visit)
            .filter(expensive_condition)               # Expensive check first
            .filter(lambda v: v.clinician is not None)  # Simple check last
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Inefficient constraint'))
```

## 6. Debugging and Troubleshooting

### 1. **Score Corruption Detection**
```python
solver_config = SolverConfig(
    # ... other config ...
    score_director_factory_config=ScoreDirectorFactoryConfig(
        constraint_provider_function=constraints.define_constraints,
        assert_expected_undo_move_score=True  # Enables score corruption detection
    )
)
```

### 2. **Constraint Debugging**
```python
def debug_constraint(cf):
    def debug_filter(visit):
        result = visit.clinician is not None and visit.required_skill not in visit.clinician.skills
        if result:
            print(f"Skill mismatch: {visit.patient_name} needs {visit.required_skill}, "
                  f"{visit.clinician.name} has {visit.clinician.skills}")
        return result
    
    return (cf.for_each(Visit)
            .filter(debug_filter)
            .penalize(HardSoftScore.ONE_HARD)
            .as_constraint('Skill mismatch'))
```

### 3. **Solution Validation**
```python
def validate_solution(schedule):
    for visit in schedule.visits:
        if visit.clinician is None:
            print(f"Unassigned visit: {visit.patient_name}")
        if visit.start is None:
            print(f"Unscheduled visit: {visit.patient_name}")
        
        # Check hard constraints manually
        if visit.clinician and visit.required_skill not in visit.clinician.skills:
            print(f"VIOLATION: Skill mismatch for {visit.patient_name}")
```

This technical guide provides the deep implementation details needed to understand and extend the TimeFold system effectively.
