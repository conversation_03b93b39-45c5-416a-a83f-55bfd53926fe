"""
Healthcare Scheduling Engine - Medical Scheduling Implementation
===============================================================

This module provides the healthcare-specific scheduling engine that extends
the core scheduling framework with medical constraints, Google Maps integration,
and real-time planning capabilities for home healthcare agencies.

Key Features:
- Google Maps integration for real travel times
- Real-time planning for emergencies and changes
- Medical compliance and safety protocols
- Timezone-aware scheduling for multi-region operations
"""

import datetime
from typing import List, Optional, Dict, Any, Type

from core.solver import SchedulingEngine, SchedulingRequest, SchedulingResult
from core.domain import Location
from .domain import Clinician, HealthcareTask
from .constraints import HealthcareConstraintProvider
from .integrations import GoogleMapsHealthcareService


class HealthcareSchedulingEngine(SchedulingEngine):
    """
    Healthcare-specific scheduling engine with Google Maps integration
    and real-time planning capabilities for home healthcare agencies
    """
    
    def __init__(self, google_api_key: Optional[str] = None):
        """
        Initialize healthcare scheduling engine
        
        Args:
            google_api_key: Google Maps API key for travel optimization
        """
        # Initialize with healthcare constraint provider
        super().__init__(HealthcareConstraintProvider)
        
        # Initialize Google Maps service for travel optimization
        self.maps_service = None
        if google_api_key:
            self.maps_service = GoogleMapsHealthcareService(google_api_key)
    
    def _get_entity_classes(self) -> List[Type]:
        """Get the planning entity classes for healthcare domain"""
        return [HealthcareTask]
    
    def create_schedule(self, request: SchedulingRequest) -> SchedulingResult:
        """
        Create optimized healthcare schedule with travel optimization
        
        Args:
            request: Healthcare scheduling request
            
        Returns:
            SchedulingResult with travel summary and healthcare-specific metrics
        """
        # Pre-process request for healthcare-specific optimizations
        processed_request = self._preprocess_healthcare_request(request)
        
        # Create base schedule using core engine
        result = super().create_schedule(processed_request)
        
        # Add healthcare-specific enhancements
        result = self._enhance_healthcare_result(result, request)
        
        return result
    
    def _preprocess_healthcare_request(self, request: SchedulingRequest) -> SchedulingRequest:
        """Preprocess request with healthcare-specific optimizations"""
        # Sort tasks by priority (emergencies first)
        emergency_tasks = [t for t in request.tasks if t.is_emergency()]
        routine_tasks = [t for t in request.tasks if not t.is_emergency()]
        
        # Prioritize emergency tasks in the task list
        prioritized_tasks = emergency_tasks + routine_tasks
        
        # Update request with prioritized tasks
        processed_request = SchedulingRequest(
            resources=request.resources,
            tasks=prioritized_tasks,
            locations=request.locations,
            schedule_date=request.schedule_date,
            optimization_time_seconds=request.optimization_time_seconds,
            include_travel_optimization=request.include_travel_optimization,
            constraint_weights=request.constraint_weights,
            metadata=request.metadata
        )
        
        return processed_request
    
    def _enhance_healthcare_result(self, result: SchedulingResult, 
                                 original_request: SchedulingRequest) -> SchedulingResult:
        """Enhance result with healthcare-specific metrics and travel data"""
        
        # Calculate travel summary if Google Maps is available
        if self.maps_service and original_request.include_travel_optimization:
            result.travel_summary = self._calculate_travel_summary(result.schedule)
        
        # Add healthcare-specific statistics
        healthcare_stats = self._calculate_healthcare_statistics(result.schedule)
        result.statistics.update(healthcare_stats)
        
        # Generate healthcare-specific recommendations
        healthcare_recommendations = self._generate_healthcare_recommendations(result.schedule)
        result.recommendations.extend(healthcare_recommendations)
        
        return result
    
    def _calculate_travel_summary(self, schedule) -> Dict[str, Any]:
        """Calculate travel summary using Google Maps data"""
        if not self.maps_service:
            return {}
        
        total_distance_km = 0.0
        total_duration_hours = 0.0
        total_fuel_cost = 0.0
        
        # Group tasks by resource to calculate routes
        resource_tasks = {}
        for task in schedule.tasks:
            if task.assigned_resource and task.location:
                resource = task.assigned_resource
                if resource not in resource_tasks:
                    resource_tasks[resource] = []
                resource_tasks[resource].append(task)
        
        # Calculate travel for each resource
        for resource, tasks in resource_tasks.items():
            if len(tasks) < 2:
                continue
            
            # Sort tasks by start time
            sorted_tasks = sorted(tasks, key=lambda t: t.start_time or datetime.datetime.min)
            
            # Calculate route through all patient homes
            locations = [task.location for task in sorted_tasks if task.location]
            if len(locations) >= 2:
                route_info = self.maps_service.optimize_healthcare_route(
                    start_location=resource.location or locations[0],
                    patient_locations=locations,
                    end_location=resource.location or locations[-1]
                )
                
                if route_info:
                    total_distance_km += route_info.get('total_distance_km', 0)
                    total_duration_hours += route_info.get('total_duration_hours', 0)
                    total_fuel_cost += route_info.get('estimated_fuel_cost', 0)
        
        return {
            'total_distance_km': total_distance_km,
            'total_duration_hours': total_duration_hours,
            'estimated_total_fuel_cost': total_fuel_cost,
            'average_distance_per_resource': total_distance_km / len(resource_tasks) if resource_tasks else 0,
            'travel_efficiency_score': self._calculate_travel_efficiency_score(total_distance_km, len(resource_tasks))
        }
    
    def _calculate_travel_efficiency_score(self, total_distance: float, resource_count: int) -> float:
        """Calculate travel efficiency score (0-100, higher is better)"""
        if resource_count == 0:
            return 100.0
        
        avg_distance_per_resource = total_distance / resource_count
        
        # Excellent: < 20km per resource, Poor: > 80km per resource
        if avg_distance_per_resource <= 20:
            return 100.0
        elif avg_distance_per_resource >= 80:
            return 20.0
        else:
            # Linear scale between 20km (100 points) and 80km (20 points)
            return 100 - ((avg_distance_per_resource - 20) / 60) * 80
    
    def _calculate_healthcare_statistics(self, schedule) -> Dict[str, Any]:
        """Calculate healthcare-specific statistics"""
        stats = {}
        
        # Emergency response metrics
        emergency_tasks = [t for t in schedule.tasks if t.is_emergency()]
        assigned_emergencies = [t for t in emergency_tasks if t.assigned_resource]
        
        stats['emergency_tasks'] = len(emergency_tasks)
        stats['assigned_emergencies'] = len(assigned_emergencies)
        stats['emergency_assignment_rate'] = (
            len(assigned_emergencies) / len(emergency_tasks) * 100 
            if emergency_tasks else 100.0
        )
        
        # Continuity of care metrics
        patient_assignments = {}
        for task in schedule.tasks:
            if task.patient_id and task.assigned_resource:
                if task.patient_id not in patient_assignments:
                    patient_assignments[task.patient_id] = set()
                patient_assignments[task.patient_id].add(task.assigned_resource.id)
        
        continuity_violations = sum(1 for resources in patient_assignments.values() if len(resources) > 1)
        stats['continuity_violations'] = continuity_violations
        stats['continuity_score'] = (
            (len(patient_assignments) - continuity_violations) / len(patient_assignments) * 100
            if patient_assignments else 100.0
        )
        
        # Resource specialization metrics
        specialized_assignments = 0
        total_assignments = 0
        
        for task in schedule.tasks:
            if task.assigned_resource:
                total_assignments += 1
                task_complexity = task.get_clinical_complexity_score()
                resource_skill_level = max([s.level.value for s in task.assigned_resource.skills], default=1)
                
                # Good match if resource skill level matches task complexity
                if abs(resource_skill_level - task_complexity/2) <= 1:
                    specialized_assignments += 1
        
        stats['specialization_match_rate'] = (
            specialized_assignments / total_assignments * 100 
            if total_assignments else 100.0
        )
        
        # Safety and compliance metrics
        high_risk_tasks = [t for t in schedule.tasks 
                          if t.location and t.location.safety_rating <= 2]
        stats['high_risk_assignments'] = len([t for t in high_risk_tasks if t.assigned_resource])
        
        return stats
    
    def _generate_healthcare_recommendations(self, schedule) -> List[str]:
        """Generate healthcare-specific recommendations"""
        recommendations = []
        
        # Check for unassigned emergency tasks
        unassigned_emergencies = [t for t in schedule.tasks 
                                if t.is_emergency() and not t.assigned_resource]
        if unassigned_emergencies:
            recommendations.append(
                f"URGENT: {len(unassigned_emergencies)} emergency tasks unassigned. "
                f"Consider emergency staffing or overtime authorization."
            )
        
        # Check for continuity violations
        patient_continuity = {}
        for task in schedule.tasks:
            if task.patient_id and task.assigned_resource:
                if task.patient_id not in patient_continuity:
                    patient_continuity[task.patient_id] = set()
                patient_continuity[task.patient_id].add(task.assigned_resource.id)
        
        continuity_issues = [pid for pid, resources in patient_continuity.items() 
                           if len(resources) > 1]
        if continuity_issues:
            recommendations.append(
                f"Consider reassigning tasks for {len(continuity_issues)} patients "
                f"to improve continuity of care."
            )
        
        # Check for travel efficiency
        if hasattr(schedule, 'travel_summary') and schedule.travel_summary:
            efficiency = schedule.travel_summary.get('travel_efficiency_score', 100)
            if efficiency < 60:
                recommendations.append(
                    f"Travel efficiency is low ({efficiency:.1f}/100). "
                    f"Consider geographic clustering of assignments."
                )
        
        # Check for workload balance
        resource_workloads = {}
        for task in schedule.tasks:
            if task.assigned_resource:
                resource_id = task.assigned_resource.id
                resource_workloads[resource_id] = resource_workloads.get(resource_id, 0) + 1
        
        if resource_workloads:
            max_workload = max(resource_workloads.values())
            min_workload = min(resource_workloads.values())
            
            if max_workload - min_workload > 3:
                recommendations.append(
                    f"Workload imbalance detected (range: {min_workload}-{max_workload} tasks). "
                    f"Consider redistributing assignments."
                )
        
        return recommendations
    
    def handle_emergency_insertion(self, current_schedule, emergency_task: HealthcareTask) -> SchedulingResult:
        """
        Handle emergency task insertion into existing schedule
        
        Args:
            current_schedule: Current optimized schedule
            emergency_task: Emergency task to insert
            
        Returns:
            Updated schedule with emergency task
        """
        # Add emergency task to the schedule
        updated_tasks = current_schedule.tasks + [emergency_task]
        
        # Create emergency scheduling request with shorter optimization time
        emergency_request = SchedulingRequest(
            resources=current_schedule.resources,
            tasks=updated_tasks,
            locations=current_schedule.locations,
            optimization_time_seconds=5,  # Quick optimization for emergencies
            include_travel_optimization=True
        )
        
        # Re-optimize with emergency priority
        return self.create_schedule(emergency_request)
    
    def update_schedule_for_staff_illness(self, current_schedule,
                                        sick_resource: Clinician,
                                        unavailable_until: datetime.datetime) -> SchedulingResult:
        """
        Update schedule when staff member calls in sick
        
        Args:
            current_schedule: Current schedule
            sick_resource: Resource that is now unavailable
            unavailable_until: When resource will be available again
            
        Returns:
            Updated schedule with reassigned tasks
        """
        # Mark resource as sick
        sick_resource.mark_sick(unavailable_until, "illness")
        
        # Find tasks assigned to sick resource
        affected_tasks = [t for t in current_schedule.tasks 
                         if t.assigned_resource == sick_resource]
        
        # Unassign affected tasks for re-optimization
        for task in affected_tasks:
            task.assigned_resource = None
            task.start_time = None
        
        # Create re-optimization request
        reoptimization_request = SchedulingRequest(
            resources=current_schedule.resources,
            tasks=current_schedule.tasks,
            locations=current_schedule.locations,
            optimization_time_seconds=15,  # Moderate time for staff changes
            include_travel_optimization=True
        )
        
        return self.create_schedule(reoptimization_request)
